/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(rsc)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNhZG1pbi1wYW5lbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBaUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcYWRtaW4tcGFuZWxcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"962e3d2093e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NjJlM2QyMDkzZTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n\n\n\n\n// UI components\n\n\nconst metadata = {\n    title: \"Security Token Admin Panel\",\n    description: \"Admin panel for managing ERC-3643 security tokens\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased bg-gray-50 min-h-screen`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_3__.Providers, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-grow container mx-auto px-4 py-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: \"bg-gray-800 text-white py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"container mx-auto px-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" Security Token Admin Panel\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\admin-panel\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\admin-panel\\src\\components\\Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\github\\tokendev-newroo\\admin-panel\\src\\components\\Providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNnaXRodWIlNUMlNUN0b2tlbmRldi1uZXdyb28lNUMlNUNhZG1pbi1wYW5lbCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBaUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGdpdGh1YlxcXFx0b2tlbmRldi1uZXdyb29cXFxcYWRtaW4tcGFuZWxcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cgithub%5C%5Ctokendev-newroo%5C%5Cadmin-panel%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config */ \"(ssr)/./src/config.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contracts/SecurityTokenFactory.json */ \"(ssr)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contracts/SecurityToken.json */ \"(ssr)/./src/contracts/SecurityToken.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [network, setNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('amoy');\n    const [factoryAddress, setFactoryAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tokenImplementation, setTokenImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistImplementation, setWhitelistImplementation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationAddress, setVerificationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [walletConnected, setWalletConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadMethod, setLoadMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('logs');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Get contract addresses for current network\n            const addresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            setFactoryAddress(addresses.factory || '');\n            setTokenImplementation(addresses.tokenImplementation || '');\n            setWhitelistImplementation(addresses.whitelistImplementation || '');\n            checkWalletConnection();\n        }\n    }[\"Home.useEffect\"], [\n        network\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (walletConnected) {\n                fetchTokens();\n            } else {\n                // Load known tokens even without wallet connection for display\n                loadKnownTokensOnly();\n            }\n        }\n    }[\"Home.useEffect\"], [\n        walletConnected,\n        network\n    ]);\n    // Function to load only known tokens (for display without wallet)\n    const loadKnownTokensOnly = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n            if (knownTokens.length > 0) {\n                console.log(`Loading ${knownTokens.length} known tokens for display...`);\n                // Create basic token objects from known tokens\n                const basicTokens = knownTokens.map((knownToken)=>({\n                        address: knownToken.address,\n                        name: knownToken.name,\n                        symbol: knownToken.symbol,\n                        maxSupply: \"Connect wallet to view\",\n                        totalSupply: \"Connect wallet to view\",\n                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                        tokenImageUrl: \"\"\n                    }));\n                setTokens(basicTokens);\n                setError(\"Connect your wallet to view live token data and access management features.\");\n            } else {\n                setTokens([]);\n                setError(\"No known tokens configured for this network. Connect your wallet to discover tokens from the factory.\");\n            }\n        } catch (error) {\n            console.error(\"Error loading known tokens:\", error);\n            setError(\"Error loading token information. Please try again.\");\n        }\n        setIsLoading(false);\n    };\n    const checkWalletConnection = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                const accounts = await provider.listAccounts();\n                if (accounts.length > 0) {\n                    setWalletConnected(true);\n                } else {\n                    setWalletConnected(false);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error checking wallet connection:\", error);\n            setWalletConnected(false);\n        }\n    };\n    const connectWallet = async ()=>{\n        try {\n            if (window.ethereum) {\n                const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n                await provider.send(\"eth_requestAccounts\", []);\n                setWalletConnected(true);\n            } else {\n                setError(\"Please install MetaMask to use this feature!\");\n            }\n        } catch (error) {\n            console.error(\"Error connecting wallet:\", error);\n        }\n    };\n    // Add your token address to verify\n    const addTokenManually = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address');\n            return;\n        }\n        if (!ethers__WEBPACK_IMPORTED_MODULE_8__.isAddress(verificationAddress)) {\n            alert('Please enter a valid address');\n            return;\n        }\n        loadTokenByAddress(verificationAddress);\n    };\n    const loadTokenByAddress = async (address)=>{\n        try {\n            setIsLoading(true);\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            // Use the new loadTokenDetails function\n            const tokenDetails = await loadTokenDetails(address);\n            if (tokenDetails) {\n                // Check if the token is already in the list\n                const exists = tokens.some((token)=>token.address.toLowerCase() === address.toLowerCase());\n                if (!exists) {\n                    setTokens((prevTokens)=>[\n                            ...prevTokens,\n                            tokenDetails\n                        ]);\n                }\n                setVerificationAddress('');\n            } else {\n                alert(\"Could not load token details. Is this a valid Security Token address?\");\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error loading token:', err);\n            setError(err.message || 'Error loading token. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const loadTokenDetails = async (address)=>{\n        try {\n            if (!window.ethereum) {\n                throw new Error('MetaMask not available');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            // Get the token contract\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_5__.abi, provider);\n            // Read token details\n            const name = await tokenContract.name();\n            const symbol = await tokenContract.symbol();\n            // Get decimals to format amounts correctly\n            const decimalsRaw = await tokenContract.decimals();\n            const decimals = Number(decimalsRaw);\n            let maxSupply = \"0\";\n            let totalSupply = \"0\";\n            let whitelistAddress = \"******************************************\";\n            try {\n                const maxSupplyRaw = await tokenContract.maxSupply();\n                maxSupply = decimals === 0 ? maxSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(maxSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read maxSupply:\", err);\n            }\n            try {\n                const totalSupplyRaw = await tokenContract.totalSupply();\n                totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_10__.formatUnits(totalSupplyRaw, decimals);\n            } catch (err) {\n                console.warn(\"Could not read totalSupply:\", err);\n            }\n            try {\n                whitelistAddress = await tokenContract.identityRegistry();\n            } catch (err) {\n                try {\n                    whitelistAddress = await tokenContract.whitelistAddress();\n                } catch (err2) {\n                    console.warn(\"Could not read whitelist address:\", err2);\n                }\n            }\n            // Try to get token image URL if supported\n            let tokenImageUrl = \"\";\n            try {\n                tokenImageUrl = await tokenContract.tokenImageUrl();\n            } catch (err) {\n                console.log(\"Token doesn't support image URL or image URL is empty\");\n            }\n            return {\n                address,\n                name,\n                symbol,\n                maxSupply,\n                totalSupply,\n                whitelistAddress,\n                tokenImageUrl\n            };\n        } catch (error) {\n            console.error(`Error loading token details for ${address}:`, error);\n            return null;\n        }\n    };\n    const fetchTokens = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        setTokens([]);\n        try {\n            if (!window.ethereum) {\n                throw new Error('Please install MetaMask to use this feature!');\n            }\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_7__.BrowserProvider(window.ethereum);\n            const networkConfig = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getNetworkConfig)(network);\n            const contractAddresses = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getContractAddresses)(network);\n            if (!contractAddresses.factory) {\n                throw new Error(`No factory address configured for network: ${network}`);\n            }\n            // Connect to the factory contract\n            const factory = new ethers__WEBPACK_IMPORTED_MODULE_9__.Contract(contractAddresses.factory, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__.abi, provider);\n            console.log(\"Loading tokens from factory:\", contractAddresses.factory);\n            console.log(\"Network:\", network);\n            console.log(\"Provider:\", provider);\n            try {\n                // Try to get tokens using the new enumeration methods\n                console.log(\"Attempting to fetch tokens using factory enumeration...\");\n                try {\n                    // First, try to get the token count (new factory enumeration)\n                    const tokenCount = await factory.getTokenCount();\n                    console.log(`Factory reports ${tokenCount} deployed tokens`);\n                    if (tokenCount > 0) {\n                        // Get all token addresses at once for better performance\n                        const tokenAddresses = await factory.getAllDeployedTokens();\n                        console.log(\"Retrieved token addresses:\", tokenAddresses);\n                        // Load details for each token\n                        const tokenPromises = tokenAddresses.map(async (address)=>{\n                            try {\n                                return await loadTokenDetails(address);\n                            } catch (error) {\n                                console.warn(`Failed to load token details for ${address}:`, error);\n                                return null;\n                            }\n                        });\n                        const tokenResults = await Promise.all(tokenPromises);\n                        const validTokens = tokenResults.filter((token)=>token !== null);\n                        if (validTokens.length > 0) {\n                            setTokens(validTokens);\n                            console.log(`Successfully loaded ${validTokens.length} tokens from factory`);\n                        } else {\n                            setError(\"Factory has tokens but could not load their details. Please check network connection.\");\n                        }\n                    } else {\n                        setError(\"No tokens found in factory. Create your first token to see it here.\");\n                    }\n                } catch (enumerationError) {\n                    console.warn(\"Factory enumeration failed, trying event-based discovery:\", enumerationError);\n                    // Fallback: Use event-based token discovery for older factories\n                    try {\n                        console.log(\"Searching for TokenDeployed events...\");\n                        // Get TokenDeployed events from the factory\n                        const filter = factory.filters.TokenDeployed();\n                        let events = [];\n                        // Try different block ranges to find events\n                        const ranges = [\n                            -10000,\n                            -50000\n                        ]; // Last 10k, then 50k blocks\n                        for (const range of ranges){\n                            try {\n                                events = await factory.queryFilter(filter, range);\n                                if (events.length > 0) break; // Found events, stop searching\n                            } catch (error) {\n                                console.warn(`Failed to query ${Math.abs(range)} blocks:`, error);\n                            }\n                        }\n                        console.log(`Found ${events.length} TokenDeployed events`);\n                        if (events.length > 0) {\n                            // Extract unique token addresses from events\n                            const tokenAddresses = [\n                                ...new Set(events.map((event)=>event.args?.tokenAddress).filter(Boolean))\n                            ];\n                            console.log(\"Token addresses from events:\", tokenAddresses);\n                            // Load details for each token\n                            const tokenPromises = tokenAddresses.map(async (address)=>{\n                                try {\n                                    return await loadTokenDetails(address);\n                                } catch (error) {\n                                    console.warn(`Failed to load token details for ${address}:`, error);\n                                    return null;\n                                }\n                            });\n                            const tokenResults = await Promise.all(tokenPromises);\n                            const validTokens = tokenResults.filter((token)=>token !== null);\n                            if (validTokens.length > 0) {\n                                setTokens(validTokens);\n                                console.log(`Successfully loaded ${validTokens.length} tokens from events`);\n                            } else {\n                                setError(\"Found token events but could not load token details. Please check network connection.\");\n                            }\n                        } else {\n                            setError(\"No tokens found in factory events. Create your first token to see it here.\");\n                        }\n                    } catch (eventError) {\n                        console.warn(\"Event-based discovery failed:\", eventError);\n                        // Final fallback: Load known tokens from configuration\n                        console.log(\"Falling back to known tokens from configuration...\");\n                        const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                        if (knownTokens.length > 0) {\n                            console.log(`Loading ${knownTokens.length} known tokens...`);\n                            const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                                try {\n                                    const tokenDetails = await loadTokenDetails(knownToken.address);\n                                    return tokenDetails;\n                                } catch (error) {\n                                    console.warn(`Failed to load known token ${knownToken.address}:`, error);\n                                    // Return basic info even if contract call fails\n                                    return {\n                                        address: knownToken.address,\n                                        name: knownToken.name,\n                                        symbol: knownToken.symbol,\n                                        maxSupply: \"Unknown\",\n                                        totalSupply: \"Unknown\",\n                                        whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                        tokenImageUrl: \"\"\n                                    };\n                                }\n                            });\n                            const knownTokenResults = await Promise.all(knownTokenPromises);\n                            const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                            if (validKnownTokens.length > 0) {\n                                setTokens(validKnownTokens);\n                                console.log(`Successfully loaded ${validKnownTokens.length} known tokens`);\n                                setError(\"Factory enumeration failed, but loaded known tokens. You can add more tokens manually below.\");\n                            } else {\n                                setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the tool below.\");\n                            }\n                        } else {\n                            setError(\"Could not load tokens from factory. Please add token addresses manually using the tool below.\");\n                        }\n                    }\n                }\n            } catch (err) {\n                console.error(\"Error loading tokens:\", err);\n                const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n                // Try to load known tokens as fallback even on factory connection error\n                console.log(\"Factory connection failed, trying known tokens...\");\n                const knownTokens = (0,_config__WEBPACK_IMPORTED_MODULE_3__.getKnownTokens)(network);\n                if (knownTokens.length > 0) {\n                    try {\n                        const knownTokenPromises = knownTokens.map(async (knownToken)=>{\n                            try {\n                                const tokenDetails = await loadTokenDetails(knownToken.address);\n                                return tokenDetails;\n                            } catch (error) {\n                                console.warn(`Failed to load known token ${knownToken.address}:`, error);\n                                return {\n                                    address: knownToken.address,\n                                    name: knownToken.name,\n                                    symbol: knownToken.symbol,\n                                    maxSupply: \"Unknown\",\n                                    totalSupply: \"Unknown\",\n                                    whitelistAddress: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                                    tokenImageUrl: \"\"\n                                };\n                            }\n                        });\n                        const knownTokenResults = await Promise.all(knownTokenPromises);\n                        const validKnownTokens = knownTokenResults.filter((token)=>token !== null);\n                        if (validKnownTokens.length > 0) {\n                            setTokens(validKnownTokens);\n                            console.log(`Loaded ${validKnownTokens.length} known tokens as fallback`);\n                            setError(\"Factory connection failed, but loaded known tokens. Please check your network connection.\");\n                        } else {\n                            setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                        }\n                    } catch (fallbackError) {\n                        console.error(\"Fallback token loading failed:\", fallbackError);\n                        setError(\"Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.\");\n                    }\n                } else {\n                    setError(\"Could not load tokens from factory. Please add token addresses manually using the verify tool below.\");\n                }\n            }\n            setIsLoading(false);\n        } catch (err) {\n            console.error('Error fetching tokens:', err);\n            setError(err.message || 'Error fetching tokens. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const handleNetworkChange = (event)=>{\n        const newNetwork = event.target.value;\n        console.log(`Switching to network: ${newNetwork}`);\n        setNetwork(newNetwork);\n    };\n    const verifyToken = ()=>{\n        if (!verificationAddress) {\n            alert('Please enter a token address to verify');\n            return;\n        }\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = `https://www.oklink.com/amoy/address/${verificationAddress}`;\n        } else if (network === 'polygon') {\n            explorerUrl = `https://polygonscan.com/address/${verificationAddress}`;\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const getBlockExplorerUrl = (network)=>{\n        if (network === 'amoy') {\n            return 'https://www.oklink.com/amoy';\n        } else if (network === 'polygon') {\n            return 'https://polygonscan.com';\n        }\n        return '#';\n    };\n    const viewFactoryTokens = ()=>{\n        let explorerUrl = '';\n        if (network === 'amoy') {\n            explorerUrl = `https://www.oklink.com/amoy/address/${factoryAddress}#eventlog`;\n        } else if (network === 'polygon') {\n            explorerUrl = `https://polygonscan.com/address/${factoryAddress}#events`;\n        }\n        if (explorerUrl) {\n            window.open(explorerUrl, '_blank');\n        }\n    };\n    const formatNumber = (value)=>{\n        // Format the number with commas\n        const number = parseFloat(value);\n        return number.toLocaleString(undefined, {\n            maximumFractionDigits: 0\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Security Token Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"View and manage your security tokens\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"network\",\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"network\",\n                                className: \"p-2 border border-gray-300 rounded-md\",\n                                value: network,\n                                onChange: handleNetworkChange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"amoy\",\n                                        children: \"Amoy Testnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"polygon\",\n                                        children: \"Polygon Mainnet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: connectWallet,\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Connect Wallet\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition\",\n                                children: \"Create New Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-bold mb-2\",\n                        children: \"Connected to Your Deployed Factory Contract\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Factory Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: factoryAddress\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            tokenImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Token Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: tokenImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this),\n                            whitelistImplementation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Whitelist Implementation:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-sm\",\n                                        children: whitelistImplementation\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: `${getBlockExplorerUrl(network)}/address/${factoryAddress}`,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-green-700 underline hover:text-green-900\",\n                                children: [\n                                    \"View Factory on \",\n                                    network === 'amoy' ? 'OKLink Explorer' : 'PolygonScan'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: viewFactoryTokens,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm\",\n                                children: \"View All Factory Token Deployments\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDE80 Create Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/modular-tokens\",\n                                className: \"bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD12 Manage Tokens\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/clients\",\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDC65 Client Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.open('https://amoy.polygonscan.com/', '_blank'),\n                                className: \"bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded text-sm font-medium\",\n                                children: \"\\uD83D\\uDD0D Block Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 610,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"Add Token to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4 text-gray-600\",\n                        children: \"Manually add a token by entering its address below.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 638,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: verificationAddress,\n                                onChange: (e)=>setVerificationAddress(e.target.value),\n                                placeholder: \"Enter token address (0x...)\",\n                                className: \"flex-grow p-2 border border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: addTokenManually,\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                                children: \"Add Token\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: verifyToken,\n                                className: \"bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded\",\n                                children: \"Verify on Explorer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-xs text-gray-500\",\n                        children: \"Note: After creating a token, you can add it here to display it on the dashboard.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 662,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg overflow-hidden mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Security Token Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 15\n                                            }, this),\n                                            walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: [\n                                                    \"Live data from \",\n                                                    network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet',\n                                                    \" factory\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: \"Showing known tokens - connect wallet for live data\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 677,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-3 h-3 rounded-full ${walletConnected ? 'bg-green-500' : 'bg-yellow-500'}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: walletConnected ? 'Connected' : 'Offline Mode'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: walletConnected ? fetchTokens : loadKnownTokensOnly,\n                                        className: \"text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded\",\n                                        disabled: isLoading,\n                                        children: isLoading ? 'Loading...' : 'Refresh Token List'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    !walletConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: connectWallet,\n                                        className: \"text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded\",\n                                        children: \"Connect for Live Data\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center my-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 711,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 710,\n                        columnNumber: 11\n                    }, this) : tokens.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Total Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Max Supply\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Source\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: tokens.map((token, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: [\n                                                                token.tokenImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    className: \"h-10 w-10 rounded-full object-cover border-2 border-gray-200\",\n                                                                    src: token.tokenImageUrl,\n                                                                    alt: `${token.name} logo`,\n                                                                    onError: (e)=>{\n                                                                        // Fallback to default icon if image fails to load\n                                                                        const target = e.target;\n                                                                        target.style.display = 'none';\n                                                                        target.nextElementSibling?.classList.remove('hidden');\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 27\n                                                                }, this) : null,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center ${token.tokenImageUrl ? 'hidden' : ''}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-gray-700\",\n                                                                        children: token.symbol.substring(0, 2).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: token.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 truncate\",\n                                                                    title: token.address,\n                                                                    children: [\n                                                                        token.address.substring(0, 10),\n                                                                        \"...\",\n                                                                        token.address.substring(token.address.length - 8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.symbol\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.totalSupply ? formatNumber(token.totalSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: token.maxSupply ? formatNumber(token.maxSupply) : \"—\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                                                    children: walletConnected && token.totalSupply !== \"Connect wallet to view\" ? (0,_config__WEBPACK_IMPORTED_MODULE_3__.isKnownToken)(network, token.address) ? 'Known + Live' : 'Factory' : 'Known Token'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: `/tokens/${token.address}`,\n                                                        className: \"text-blue-600 hover:text-blue-900 mr-4\",\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: getBlockExplorerUrl(network) + '/address/' + token.address,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-gray-600 hover:text-gray-900\",\n                                                        children: \"Explorer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: walletConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: [\n                                        \"No tokens found on \",\n                                        network,\n                                        \" network.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: 'To add a token you\\'ve created, use the \"Add Token to Dashboard\" section above.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/modular-tokens\",\n                                    className: \"text-blue-600 hover:text-blue-900 font-medium\",\n                                    children: \"Create your first token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: connectWallet,\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded\",\n                            children: \"Connect Wallet to View Tokens\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 818,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-md rounded-lg p-6 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/modular-tokens\",\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded inline-block\",\n                    children: [\n                        \"Create New Token on \",\n                        network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet'\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 845,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 844,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectWallet.tsx":
/*!******************************************!*\
  !*** ./src/components/ConnectWallet.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(ssr)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ConnectWallet = ()=>{\n    const [account, setAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [networkName, setNetworkName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isConnecting, setIsConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectWallet.useEffect\": ()=>{\n            // Check if already connected\n            checkIfWalletIsConnected();\n        }\n    }[\"ConnectWallet.useEffect\"], []);\n    // Listen for account changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConnectWallet.useEffect\": ()=>{\n            if (window.ethereum) {\n                window.ethereum.on('accountsChanged', {\n                    \"ConnectWallet.useEffect\": (accounts)=>{\n                        if (accounts.length > 0) {\n                            setAccount(accounts[0]);\n                        } else {\n                            setAccount(null);\n                        }\n                    }\n                }[\"ConnectWallet.useEffect\"]);\n                window.ethereum.on('chainChanged', {\n                    \"ConnectWallet.useEffect\": (_chainId)=>{\n                        window.location.reload();\n                    }\n                }[\"ConnectWallet.useEffect\"]);\n            }\n            return ({\n                \"ConnectWallet.useEffect\": ()=>{\n                    if (window.ethereum) {\n                        window.ethereum.removeAllListeners();\n                    }\n                }\n            })[\"ConnectWallet.useEffect\"];\n        }\n    }[\"ConnectWallet.useEffect\"], []);\n    const checkIfWalletIsConnected = async ()=>{\n        try {\n            if (!window.ethereum) {\n                console.log('Make sure you have MetaMask installed!');\n                return;\n            }\n            // Get the provider\n            const web3Provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(window.ethereum);\n            setProvider(web3Provider);\n            // Get the network\n            const network = await web3Provider.getNetwork();\n            setNetworkName(network.name === 'unknown' ? `Chain ID ${network.chainId}` : network.name);\n            // Get accounts\n            const accounts = await web3Provider.listAccounts();\n            if (accounts.length > 0) {\n                setAccount(accounts[0].address);\n            }\n        } catch (error) {\n            console.error('Error checking if wallet is connected:', error);\n        }\n    };\n    const connectWallet = async ()=>{\n        try {\n            setIsConnecting(true);\n            if (!window.ethereum) {\n                alert('Please install MetaMask to use this feature!');\n                setIsConnecting(false);\n                return;\n            }\n            // Request accounts\n            const web3Provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.BrowserProvider(window.ethereum);\n            await web3Provider.send('eth_requestAccounts', []);\n            // Get the connected account\n            const signer = await web3Provider.getSigner();\n            const connectedAccount = await signer.getAddress();\n            // Get the network\n            const network = await web3Provider.getNetwork();\n            setProvider(web3Provider);\n            setAccount(connectedAccount);\n            setNetworkName(network.name === 'unknown' ? `Chain ID ${network.chainId.toString()}` : network.name);\n            setIsConnecting(false);\n        } catch (error) {\n            console.error('Error connecting wallet:', error);\n            setIsConnecting(false);\n        }\n    };\n    const disconnectWallet = ()=>{\n        setAccount(null);\n        setProvider(null);\n        setNetworkName('');\n    };\n    const shortenAddress = (address)=>{\n        return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: account ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs md:text-sm bg-blue-900 px-2 py-1 rounded\",\n                    children: networkName\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded flex items-center text-sm\",\n                            children: shortenAddress(account)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute z-10 hidden group-hover:block right-0 mt-2 w-48 bg-white text-gray-800 rounded shadow-lg p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: disconnectWallet,\n                                className: \"w-full text-left px-4 py-2 hover:bg-gray-100 rounded text-sm\",\n                                children: \"Disconnect\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n            lineNumber: 108,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: connectWallet,\n            disabled: isConnecting,\n            className: \"bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm flex items-center\",\n            children: isConnecting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 15\n                    }, undefined),\n                    \"Connecting...\"\n                ]\n            }, void 0, true) : 'Connect Wallet'\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n            lineNumber: 127,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\ConnectWallet.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectWallet);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectWallet.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ConnectWallet__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ConnectWallet */ \"(ssr)/./src/components/ConnectWallet.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navItems = [\n        {\n            title: 'Dashboard',\n            path: '/'\n        },\n        {\n            title: 'Tokens',\n            path: '/tokens'\n        },\n        {\n            title: 'Modular Tokens',\n            path: '/modular-tokens'\n        },\n        {\n            title: 'Create Modular Token',\n            path: '/create-modular-token'\n        },\n        {\n            title: 'Claims',\n            path: '/claims-management'\n        },\n        {\n            title: 'Clients',\n            path: '/clients'\n        },\n        {\n            title: 'Qualifications',\n            path: '/qualifications'\n        },\n        {\n            title: 'Identity',\n            path: '/identity'\n        },\n        {\n            title: 'Orders',\n            path: '/orders'\n        },\n        {\n            title: 'Upgrade Testing',\n            path: '/upgrade-testing'\n        },\n        {\n            title: 'Upgrade Monitoring',\n            path: '/upgrade-monitoring'\n        },\n        {\n            title: 'Upgrade Deployment',\n            path: '/upgrade-deployment'\n        },\n        {\n            title: 'Deployment Dashboard',\n            path: '/deployment-dashboard'\n        },\n        {\n            title: 'API Integration',\n            path: '/api-integration'\n        },\n        {\n            title: 'API Keys',\n            path: '/api-keys'\n        },\n        {\n            title: 'External API Docs',\n            path: '/external-api-docs'\n        }\n    ];\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-gray-800 text-white shadow-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"font-bold text-xl\",\n                                children: \"Security Token Admin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-6\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.path,\n                                    className: `hover:text-blue-300 transition ${pathname === item.path ? 'text-blue-300 font-semibold' : ''}`,\n                                    children: item.title\n                                }, item.path, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectWallet__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden text-white\",\n                            onClick: toggleMobileMenu,\n                            \"aria-label\": \"Toggle menu\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 pb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.path,\n                                    className: `hover:text-blue-300 transition ${pathname === item.path ? 'text-blue-300 font-semibold' : ''}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    children: item.title\n                                }, item.path, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConnectWallet__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/wagmi/dist/esm/context.js\");\n/* harmony import */ var _config_wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/wagmi */ \"(ssr)/./src/config/wagmi.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Providers.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1\n                    }\n                }\n            })\n    }[\"Providers.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(wagmi__WEBPACK_IMPORTED_MODULE_4__.WagmiProvider, {\n        config: _config_wagmi__WEBPACK_IMPORTED_MODULE_2__.config,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Providers.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Qcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDNkI7QUFDbkM7QUFDRztBQUVsQyxTQUFTSyxVQUFVLEVBQUVDLFFBQVEsRUFBMkI7SUFDN0QsTUFBTSxDQUFDQyxZQUFZLEdBQUdQLCtDQUFRQTs4QkFBQyxJQUFNLElBQUlDLDhEQUFXQSxDQUFDO2dCQUNuRE8sZ0JBQWdCO29CQUNkQyxTQUFTO3dCQUNQQyxXQUFXLEtBQUs7d0JBQ2hCQyxPQUFPO29CQUNUO2dCQUNGO1lBQ0Y7O0lBRUEscUJBQ0UsOERBQUNSLGdEQUFhQTtRQUFDQyxRQUFRQSxpREFBTUE7a0JBQzNCLDRFQUFDRixzRUFBbUJBO1lBQUNVLFFBQVFMO3NCQUMxQkQ7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcY29tcG9uZW50c1xcUHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBSZWFjdE5vZGUsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XHJcbmltcG9ydCB7IFdhZ21pUHJvdmlkZXIgfSBmcm9tICd3YWdtaSc7XHJcbmltcG9ydCB7IGNvbmZpZyB9IGZyb20gJy4uL2NvbmZpZy93YWdtaSc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcclxuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoKCkgPT4gbmV3IFF1ZXJ5Q2xpZW50KHtcclxuICAgIGRlZmF1bHRPcHRpb25zOiB7XHJcbiAgICAgIHF1ZXJpZXM6IHtcclxuICAgICAgICBzdGFsZVRpbWU6IDYwICogMTAwMCwgLy8gMSBtaW51dGVcclxuICAgICAgICByZXRyeTogMSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgfSkpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPFdhZ21pUHJvdmlkZXIgY29uZmlnPXtjb25maWd9PlxyXG4gICAgICA8UXVlcnlDbGllbnRQcm92aWRlciBjbGllbnQ9e3F1ZXJ5Q2xpZW50fT5cclxuICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cclxuICAgIDwvV2FnbWlQcm92aWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwiV2FnbWlQcm92aWRlciIsImNvbmZpZyIsIlByb3ZpZGVycyIsImNoaWxkcmVuIiwicXVlcnlDbGllbnQiLCJkZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJzdGFsZVRpbWUiLCJyZXRyeSIsImNsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contractAddresses: () => (/* binding */ contractAddresses),\n/* harmony export */   defaultNetwork: () => (/* binding */ defaultNetwork),\n/* harmony export */   getContractAddresses: () => (/* binding */ getContractAddresses),\n/* harmony export */   getKnownTokens: () => (/* binding */ getKnownTokens),\n/* harmony export */   getNetworkConfig: () => (/* binding */ getNetworkConfig),\n/* harmony export */   isKnownToken: () => (/* binding */ isKnownToken),\n/* harmony export */   knownTokens: () => (/* binding */ knownTokens),\n/* harmony export */   networkConfig: () => (/* binding */ networkConfig),\n/* harmony export */   tokenTypes: () => (/* binding */ tokenTypes),\n/* harmony export */   verifyTokenExists: () => (/* binding */ verifyTokenExists)\n/* harmony export */ });\n// Network configuration\nconst networkConfig = {\n    // Amoy testnet\n    amoy: {\n        chainId: 80002,\n        name: \"Amoy\",\n        rpcUrl: \"https://rpc-amoy.polygon.technology\",\n        blockExplorer: \"https://www.oklink.com/amoy\"\n    },\n    // Polygon mainnet\n    polygon: {\n        chainId: 137,\n        name: \"Polygon\",\n        rpcUrl: \"https://polygon-rpc.com\",\n        blockExplorer: \"https://polygonscan.com\"\n    }\n};\n// Default network\nconst defaultNetwork = \"amoy\";\n// Contract addresses - using the newly deployed factory address\nconst contractAddresses = {\n    // Updated to use SecurityTokenFactory (working factory with deploySecurityToken function)\n    amoy: {\n        factory: \"0x010c6932B830ae6A80a1Ac517ca7B5216b294C42\",\n        tokenImplementation: \"0xA4f70C1953165be63409e1F0Ff11c2D03522E8C2\",\n        whitelistImplementation: \"0x63eeE78ccc281413272bE68d9553Ae82680a0B09\",\n        whitelistWithKYCImplementation: \"0xf7c9C30Ad2E5b72F86489f26ab66cc1E79F44A7D\"\n    },\n    polygon: {\n        factory: process.env.NEXT_PUBLIC_FACTORY_ADDRESS_POLYGON || \"0x6543210987654321098765432109876543210987\"\n    }\n};\n// Known deployed modular tokens (only modular tokens supported)\nconst knownTokens = {\n    amoy: []\n};\n// Token types for creating new tokens\nconst tokenTypes = [\n    {\n        id: \"equity\",\n        name: \"Equity\"\n    },\n    {\n        id: \"bond\",\n        name: \"Bond\"\n    },\n    {\n        id: \"debenture\",\n        name: \"Debenture\"\n    },\n    {\n        id: \"warrant\",\n        name: \"Warrant\"\n    },\n    {\n        id: \"realestate\",\n        name: \"Real Estate\"\n    },\n    {\n        id: \"carbon\",\n        name: \"Carbon Credit\"\n    },\n    {\n        id: \"commodity\",\n        name: \"Commodity\"\n    }\n];\n// Helper function to get contract addresses for the current network\nconst getContractAddresses = (network)=>{\n    return contractAddresses[network] || contractAddresses[defaultNetwork];\n};\n// Helper function to get network configuration for the current network\nconst getNetworkConfig = (network)=>{\n    return networkConfig[network] || networkConfig[defaultNetwork];\n};\n// Helper function to get known tokens for a network\nconst getKnownTokens = (network)=>{\n    return knownTokens[network] || [];\n};\n// Helper to check if a token exists in factory (in a real implementation, this would query the blockchain)\nconst verifyTokenExists = async (network, tokenAddress)=>{\n    // In a real implementation, this would:\n    // 1. Connect to the factory contract\n    // 2. Call a method or check events to verify the token's existence\n    // For demo purposes, we'll just return true\n    return true;\n};\n// Helper function to validate if an address is a known token\nconst isKnownToken = (network, tokenAddress)=>{\n    const tokens = getKnownTokens(network);\n    return tokens.some((token)=>token.address.toLowerCase() === tokenAddress.toLowerCase());\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/config/wagmi.ts":
/*!*****************************!*\
  !*** ./src/config/wagmi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chains: () => (/* binding */ chains),\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\");\n/* harmony import */ var wagmi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! wagmi */ \"(ssr)/./node_modules/viem/_esm/clients/transports/http.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/polygonAmoy.js\");\n/* harmony import */ var wagmi_chains__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! wagmi/chains */ \"(ssr)/./node_modules/viem/_esm/chains/definitions/polygon.js\");\n/* harmony import */ var wagmi_connectors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! wagmi/connectors */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var wagmi_connectors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! wagmi/connectors */ \"(ssr)/./node_modules/@wagmi/connectors/dist/esm/metaMask.js\");\n\n\n\n// Define the chains we support\nconst chains = [\n    wagmi_chains__WEBPACK_IMPORTED_MODULE_0__.polygonAmoy,\n    wagmi_chains__WEBPACK_IMPORTED_MODULE_1__.polygon\n];\n// Create wagmi config\nconst config = (0,wagmi__WEBPACK_IMPORTED_MODULE_2__.createConfig)({\n    chains,\n    connectors: [\n        (0,wagmi_connectors__WEBPACK_IMPORTED_MODULE_3__.injected)(),\n        (0,wagmi_connectors__WEBPACK_IMPORTED_MODULE_4__.metaMask)()\n    ],\n    transports: {\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_0__.polygonAmoy.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.http)('https://rpc-amoy.polygon.technology'),\n        [wagmi_chains__WEBPACK_IMPORTED_MODULE_1__.polygon.id]: (0,wagmi__WEBPACK_IMPORTED_MODULE_5__.http)('https://polygon-rpc.com')\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uZmlnL3dhZ21pLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ1M7QUFDRTtBQUVyRCwrQkFBK0I7QUFDeEIsTUFBTU0sU0FBUztJQUFDSCxxREFBV0E7SUFBRUQsaURBQU9BO0NBQUMsQ0FBUztBQUVyRCxzQkFBc0I7QUFDZixNQUFNSyxTQUFTUCxtREFBWUEsQ0FBQztJQUNqQ007SUFDQUUsWUFBWTtRQUNWSiwwREFBUUE7UUFDUkMsMERBQVFBO0tBQ1Q7SUFDREksWUFBWTtRQUNWLENBQUNOLHFEQUFXQSxDQUFDTyxFQUFFLENBQUMsRUFBRVQsMkNBQUlBLENBQUM7UUFDdkIsQ0FBQ0MsaURBQU9BLENBQUNRLEVBQUUsQ0FBQyxFQUFFVCwyQ0FBSUEsQ0FBQztJQUNyQjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcY29uZmlnXFx3YWdtaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb25maWcsIGh0dHAgfSBmcm9tICd3YWdtaSdcbmltcG9ydCB7IHBvbHlnb24sIHBvbHlnb25BbW95IH0gZnJvbSAnd2FnbWkvY2hhaW5zJ1xuaW1wb3J0IHsgaW5qZWN0ZWQsIG1ldGFNYXNrIH0gZnJvbSAnd2FnbWkvY29ubmVjdG9ycydcblxuLy8gRGVmaW5lIHRoZSBjaGFpbnMgd2Ugc3VwcG9ydFxuZXhwb3J0IGNvbnN0IGNoYWlucyA9IFtwb2x5Z29uQW1veSwgcG9seWdvbl0gYXMgY29uc3RcblxuLy8gQ3JlYXRlIHdhZ21pIGNvbmZpZ1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGNyZWF0ZUNvbmZpZyh7XG4gIGNoYWlucyxcbiAgY29ubmVjdG9yczogW1xuICAgIGluamVjdGVkKCksXG4gICAgbWV0YU1hc2soKSxcbiAgXSxcbiAgdHJhbnNwb3J0czoge1xuICAgIFtwb2x5Z29uQW1veS5pZF06IGh0dHAoJ2h0dHBzOi8vcnBjLWFtb3kucG9seWdvbi50ZWNobm9sb2d5JyksXG4gICAgW3BvbHlnb24uaWRdOiBodHRwKCdodHRwczovL3BvbHlnb24tcnBjLmNvbScpLFxuICB9LFxufSlcblxuZGVjbGFyZSBtb2R1bGUgJ3dhZ21pJyB7XG4gIGludGVyZmFjZSBSZWdpc3RlciB7XG4gICAgY29uZmlnOiB0eXBlb2YgY29uZmlnXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb25maWciLCJodHRwIiwicG9seWdvbiIsInBvbHlnb25BbW95IiwiaW5qZWN0ZWQiLCJtZXRhTWFzayIsImNoYWlucyIsImNvbmZpZyIsImNvbm5lY3RvcnMiLCJ0cmFuc3BvcnRzIiwiaWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/config/wagmi.ts\n");

/***/ }),

/***/ "(ssr)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldCompliance","type":"address"},{"indexed":true,"internalType":"address","name":"newCompliance","type":"address"}],"name":"ComplianceUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"compliance","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"emergencyPause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"emergencyUnpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"compliance_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"investorCountry","outputs":[{"internalType":"uint16","name":"","type":"uint16"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"isEmergencyPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"isFunctionPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isVerified","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"pauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"unpauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newCompliance","type":"address"}],"name":"updateCompliance","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]}');

/***/ }),

/***/ "(ssr)/./src/contracts/SecurityTokenFactory.json":
/*!*************************************************!*\
  !*** ./src/contracts/SecurityTokenFactory.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldTokenImplementation","type":"address"},{"indexed":true,"internalType":"address","name":"newTokenImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldComplianceImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newComplianceImplementation","type":"address"}],"name":"ImplementationsUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"identityRegistryAddress","type":"address"},{"indexed":true,"internalType":"address","name":"complianceAddress","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"},{"indexed":false,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"bool","name":"hasKYC","type":"bool"},{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenDeployed","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"addDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"complianceImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"deploySecurityToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"},{"internalType":"address","name":"complianceAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"},{"internalType":"bool","name":"withKYC","type":"bool"}],"name":"deploySecurityTokenWithOptions","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"},{"internalType":"address","name":"complianceAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllDeployedTokens","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getDeployedToken","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"symbol","type":"string"}],"name":"getTokenAddressBySymbol","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTokenCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"removeDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newTokenImplementation","type":"address"},{"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"},{"internalType":"address","name":"newComplianceImplementation","type":"address"}],"name":"updateImplementations","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"whitelistImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"whitelistWithKYCImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]}');

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/viem","vendor-chunks/@wagmi","vendor-chunks/@tanstack","vendor-chunks/zustand","vendor-chunks/eventemitter3","vendor-chunks/mipd","vendor-chunks/@swc","vendor-chunks/wagmi"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();