/**
 * Types for the token creation page
 */

/**
 * Form data for token creation
 */
export interface TokenFormData {
  name: string;
  symbol: string;
  decimals: number;
  maxSupply: string;
  ownerAddress: string;
  tokenPrice: string;
  currency: string;
  tokenType: string;
  bonusTiers: string;
  enableKYC: boolean;
  tokenImageUrl: string;
  selectedClaims: string[]; // Array of claim types to issue
  issuerCountry: string; // Country for claim generation
}

/**
 * Deployed token information
 */
export interface DeployedToken {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  maxSupply: string;
  whitelistAddress: string;
  admin: string;
  tokenPrice: string;
  currency: string;
  bonusTiers: string;
  hasKYC: boolean;
  tokenImageUrl?: string;
}

/**
 * Deployment steps for the token creation process
 */
export type DeploymentStep =
  'idle' |
  'preparing' |
  'connecting' |
  'deploying' |
  'confirming' |
  'fetching' |
  'setting_up_compliance' |
  'completed' |
  'failed';