{"/api/freeze-tracking/route": "app/api/freeze-tracking/route.js", "/api/contracts/token/whitelist/route": "app/api/contracts/token/whitelist/route.js", "/api/tokens/route": "app/api/tokens/route.js", "/api/clients/route": "app/api/clients/route.js", "/api/whitelist/check/route": "app/api/whitelist/check/route.js", "/api/qualification-progress/route": "app/api/qualification-progress/route.js", "/tokens/page": "app/tokens/page.js", "/modular-tokens/page": "app/modular-tokens/page.js", "/modular-tokens/[address]/page": "app/modular-tokens/[address]/page.js", "/api-integration/page": "app/api-integration/page.js", "/create-modular-token/page": "app/create-modular-token/page.js"}