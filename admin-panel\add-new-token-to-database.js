const fetch = require('node-fetch');

async function addTokenToDatabase() {
    console.log('🚀 Adding newly deployed token to database...');

    // Token information from our deployment
    const tokenData = {
        address: "0xC1995ee8D14D4970206c174C5fF9276B3AB15924",
        name: "Enhanced Security Token 290755",
        symbol: "EST290755",
        decimals: 0,
        maxSupply: "1000000",
        totalSupply: "0", // Will be updated when tokens are minted
        tokenType: "equity",
        tokenPrice: "2.00 USD",
        currency: "USD",
        bonusTiers: "Early: 20%, Standard: 15%",
        network: "amoy",
        hasKYC: true,
        isActive: true,
        adminAddress: "0x56f3726C92B8B92a6ab71983886F91718540d888",
        whitelistAddress: "0xD80aF232a71Ebed586C84989307686867aC11792", // Identity Registry address
        transactionHash: "0x330a9f1e57b2265ffc4209e0212d6a986d9f7080856515bd51108d4a3ecd336c",
        deployedBy: "SecurityTokenFactory",
        deploymentNotes: "Deployed using SecurityTokenFactory with enhanced force transfer features (though not available in this implementation). Token includes graduated compliance system architecture."
    };

    try {
        console.log('📝 Token data to be added:');
        console.log(JSON.stringify(tokenData, null, 2));

        const response = await fetch('http://localhost:6677/api/tokens', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(tokenData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
        }

        const result = await response.json();
        console.log('✅ Token successfully added to database!');
        console.log('📋 Database record:');
        console.log(JSON.stringify(result, null, 2));

        console.log('\n🎉 SUCCESS! Your token is now available in the admin panel.');
        console.log('🔗 You can view it at: http://localhost:6677/tokens');
        console.log(`🔗 Direct token page: http://localhost:6677/tokens/${tokenData.address}`);
        console.log(`🔗 Blockchain explorer: https://amoy.polygonscan.com/address/${tokenData.address}`);

    } catch (error) {
        console.error('❌ Error adding token to database:', error.message);
        
        if (error.message.includes('already exists')) {
            console.log('ℹ️ Token already exists in database. This is normal if you\'ve run this script before.');
            console.log('🔗 You can still view it at: http://localhost:6677/tokens');
        } else {
            console.log('\n🔧 Troubleshooting:');
            console.log('1. Make sure the admin panel API is running on port 6677');
            console.log('2. Check that the database is properly set up');
            console.log('3. Verify the API endpoint is accessible');
        }
    }
}

// Run the script
if (require.main === module) {
    addTokenToDatabase()
        .then(() => {
            console.log('\n✨ Script completed!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Script failed:', error);
            process.exit(1);
        });
}

module.exports = addTokenToDatabase;
