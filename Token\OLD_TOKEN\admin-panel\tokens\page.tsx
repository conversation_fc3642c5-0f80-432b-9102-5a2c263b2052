'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Token {
  id: string;
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  maxSupply: string;
  tokenType: string;
  tokenPrice: string;
  currency: string;
  network: string;
  hasKYC: boolean;
  isActive: boolean;
  adminAddress?: string;
  whitelistAddress?: string;
  transactionHash?: string;
  blockNumber?: string;
  createdAt: string;
  updatedAt: string;
  version?: string;
  isModular?: boolean;
  totalSupply?: string;
}

export default function TokensPage() {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTokens();
  }, []);

  const fetchTokens = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/tokens?source=database');
      
      if (!response.ok) {
        throw new Error('Failed to fetch tokens');
      }
      
      const data = await response.json();
      setTokens(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNetworkBadgeColor = (network: string) => {
    switch (network.toLowerCase()) {
      case 'amoy':
        return 'bg-purple-100 text-purple-800';
      case 'polygon':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong className="font-bold">Error:</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Deployed Tokens</h1>
          <p className="text-gray-600 mt-2">
            Manage and view all tokens deployed through the admin panel
          </p>
        </div>
        <div className="flex gap-2">
          <Link
            href="/modular-tokens"
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Deploy Token (Modular)
          </Link>
          <Link
            href="/create-modular-token"
            className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
          >
            Deploy Modular Token (Alt)
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{tokens.length}</div>
          <div className="text-gray-600">Total Tokens</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">
            {tokens.filter(t => t.isActive).length}
          </div>
          <div className="text-gray-600">Active Tokens</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">
            {tokens.filter(t => t.hasKYC).length}
          </div>
          <div className="text-gray-600">KYC Enabled</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">
            {tokens.filter(t => t.isModular).length}
          </div>
          <div className="text-gray-600">Modular Tokens</div>
        </div>
      </div>

      {/* Tokens Table */}
      {tokens.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <div className="text-gray-500 text-lg mb-4">No tokens found</div>
          <p className="text-gray-400 mb-6">
            Deploy your first token to get started
          </p>
          <Link
            href="/modular-tokens"
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Deploy Token
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Token
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Network
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {tokens.map((token) => (
                  <tr key={token.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900 flex items-center">
                            {token.name}
                            {token.isModular && (
                              <span className="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                Modular
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {token.symbol} • {token.decimals} decimals
                            {token.totalSupply && (
                              <span className="ml-2">• Supply: {token.totalSupply}</span>
                            )}
                          </div>
                          <div className="text-xs text-gray-400 font-mono">
                            {token.address.slice(0, 10)}...{token.address.slice(-8)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getNetworkBadgeColor(token.network)}`}>
                        {token.network}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {token.tokenType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {token.tokenPrice}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          token.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {token.isActive ? 'Active' : 'Inactive'}
                        </span>
                        {token.hasKYC && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            KYC
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(token.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {token.isModular ? (
                        <Link
                          href={`/modular-tokens/${token.address}`}
                          className="text-purple-600 hover:text-purple-900 mr-4"
                        >
                          Manage
                        </Link>
                      ) : (
                        <Link
                          href={`/tokens/${token.address}`}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                        >
                          View
                        </Link>
                      )}
                      <a
                        href={`https://amoy.polygonscan.com/address/${token.address}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-600 hover:text-green-900 mr-4"
                      >
                        Contract
                      </a>
                      {token.transactionHash && (
                        <a
                          href={`https://amoy.polygonscan.com/tx/${token.transactionHash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-green-600 hover:text-green-900"
                        >
                          Tx
                        </a>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
