const fetch = require('node-fetch');

async function cleanupOldTokens() {
    console.log('🧹 Starting cleanup of old (non-modular) tokens from database...');

    // List of old token addresses to remove (non-modular tokens)
    const oldTokenAddresses = [
        "0x7544A3072FAA793e3f89048C31b794f171779544", // Advanced Control Token
        "0xfccB88D208f5Ec7166ce2291138aaD5274C671dE", // Augment_019
        "0xe5F81d7dCeB8a8F97274C749773659B7288EcF90", // Augment_01z
        "0x391a0FA1498B869d0b9445596ed49b03aA8bf46e", // Test Image Token
        "0xC1995ee8D14D4970206c174C5fF9276B3AB15924"  // Enhanced Security Token (deployed with old factory)
    ];

    try {
        console.log('📋 Tokens to be removed from database:');
        oldTokenAddresses.forEach((address, index) => {
            console.log(`   ${index + 1}. ${address}`);
        });

        // First, let's check what tokens exist in the database
        console.log('\n🔍 Checking current tokens in database...');
        
        const response = await fetch('http://localhost:6677/api/tokens', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: Failed to fetch tokens`);
        }

        const currentTokens = await response.json();
        console.log(`📊 Found ${currentTokens.length} tokens in database`);

        // Filter tokens to identify old vs modular
        const oldTokensInDb = currentTokens.filter(token => 
            oldTokenAddresses.includes(token.address)
        );

        const modularTokensInDb = currentTokens.filter(token => 
            !oldTokenAddresses.includes(token.address)
        );

        console.log(`\n📊 Database Analysis:`);
        console.log(`   🔴 Old tokens to remove: ${oldTokensInDb.length}`);
        console.log(`   ✅ Modular tokens to keep: ${modularTokensInDb.length}`);

        if (oldTokensInDb.length > 0) {
            console.log('\n🔴 Old tokens found in database:');
            oldTokensInDb.forEach(token => {
                console.log(`   - ${token.name} (${token.symbol}) - ${token.address}`);
            });
        }

        if (modularTokensInDb.length > 0) {
            console.log('\n✅ Modular tokens to preserve:');
            modularTokensInDb.forEach(token => {
                console.log(`   - ${token.name} (${token.symbol}) - ${token.address}`);
            });
        }

        // Create backup of old tokens before deletion
        console.log('\n💾 Creating backup of old tokens...');
        const backupData = {
            timestamp: new Date().toISOString(),
            removedTokens: oldTokensInDb,
            reason: 'Cleanup: Moving to modular token system only'
        };

        // Save backup to file
        const fs = require('fs');
        const backupPath = './old-tokens-backup.json';
        fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
        console.log(`✅ Backup saved to: ${backupPath}`);

        // Remove old tokens from database
        let removedCount = 0;
        for (const token of oldTokensInDb) {
            try {
                console.log(`\n🗑️  Removing token: ${token.name} (${token.address})`);
                
                const deleteResponse = await fetch(`http://localhost:6677/api/tokens/${token.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (deleteResponse.ok) {
                    console.log(`   ✅ Successfully removed: ${token.name}`);
                    removedCount++;
                } else {
                    const errorData = await deleteResponse.json();
                    console.log(`   ❌ Failed to remove ${token.name}: ${errorData.error || 'Unknown error'}`);
                }
            } catch (error) {
                console.log(`   ❌ Error removing ${token.name}: ${error.message}`);
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🎉 CLEANUP COMPLETED!');
        console.log('='.repeat(60));
        console.log(`📊 Summary:`);
        console.log(`   🗑️  Tokens removed: ${removedCount}`);
        console.log(`   ✅ Modular tokens preserved: ${modularTokensInDb.length}`);
        console.log(`   💾 Backup created: ${backupPath}`);
        
        if (removedCount === oldTokensInDb.length) {
            console.log('\n✅ All old tokens successfully removed from database!');
            console.log('🚀 Database now contains only modular tokens.');
        } else {
            console.log('\n⚠️  Some tokens could not be removed. Check the logs above.');
        }

        console.log('\n📋 Next Steps:');
        console.log('   1. ✅ Old token contracts moved to OLD_TOKEN directory');
        console.log('   2. ✅ Old admin panel components moved to OLD_TOKEN directory');
        console.log('   3. ✅ Navigation updated to remove old token references');
        console.log('   4. ✅ Database cleaned of old tokens');
        console.log('   5. 🔄 Restart admin panel to see changes');

    } catch (error) {
        console.error('❌ Cleanup failed:', error.message);
        
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Make sure the admin panel API is running on port 6677');
        console.log('2. Check that the database is accessible');
        console.log('3. Verify the API endpoints are working');
        
        process.exit(1);
    }
}

// Run the cleanup
if (require.main === module) {
    cleanupOldTokens()
        .then(() => {
            console.log('\n✨ Cleanup script completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Cleanup script failed:', error);
            process.exit(1);
        });
}

module.exports = cleanupOldTokens;
