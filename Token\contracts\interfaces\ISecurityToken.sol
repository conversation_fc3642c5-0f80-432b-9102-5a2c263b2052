// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import "@openzeppelin/contracts/access/IAccessControl.sol";

/**
 * @title ISecurityToken
 * @dev Interface for ERC-3643 compliant security tokens
 * Follows T-REX (Token for Regulated EXchanges) standard
 */
interface ISecurityToken is IERC20Metadata {
    /**
     * @dev Emitted when tokens are transferred by a forced transfer
     */
    event ForcedTransfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the identity registry address is updated
     */
    event IdentityRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);

    /**
     * @dev Emitted when token metadata is updated
     */
    event TokenMetadataUpdated(string tokenPrice, string bonusTiers, string tokenDetails);

    /**
     * @dev Emitted when max supply is updated
     */
    event MaxSupplyUpdated(uint256 oldMaxSupply, uint256 newMaxSupply);

    /**
     * @dev Emitted when token image URL is updated
     */
    event TokenImageUpdated(string tokenImageUrl);

    /**
     * @dev Emitted when an agent is added
     */
    event AgentAdded(address indexed agent);

    /**
     * @dev Emitted when an agent is removed
     */
    event AgentRemoved(address indexed agent);

    // Advanced Transfer Control Events

    /**
     * @dev Emitted when conditional transfers are enabled/disabled
     */
    event ConditionalTransfersUpdated(bool enabled);

    /**
     * @dev Emitted when a transfer is approved in the approval flow
     */
    event TransferApproved(bytes32 indexed transferId, address indexed from, address indexed to, uint256 amount);

    /**
     * @dev Emitted when transfer whitelisting is enabled/disabled
     */
    event TransferWhitelistUpdated(bool enabled);

    /**
     * @dev Emitted when an address is added/removed from transfer whitelist
     */
    event TransferWhitelistAddressUpdated(address indexed account, bool whitelisted);

    /**
     * @dev Emitted when transfer fees are enabled/disabled
     */
    event TransferFeesUpdated(bool enabled, uint256 feePercentage, address feeCollector);

    /**
     * @dev Emitted when a transfer fee is collected
     */
    event TransferFeeCollected(address indexed from, address indexed to, uint256 transferAmount, uint256 feeAmount);

    /**
     * @dev Emitted when an agreement is accepted by an address
     */
    event AgreementAccepted(address indexed account, uint256 timestamp);

    /**
     * @dev Pauses all token transfers
     */
    function pause() external;

    /**
     * @dev Unpauses all token transfers
     */
    function unpause() external;

    /**
     * @dev Mints new tokens
     * @param to The address to receive the minted tokens
     * @param amount The amount of tokens to mint
     */
    function mint(address to, uint256 amount) external;

    /**
     * @dev Force transfer tokens from one address to another (for compliance purposes)
     * This bypasses frozen checks but still requires the recipient to be whitelisted
     * @param from The address to transfer tokens from
     * @param to The address to transfer tokens to
     * @param amount The amount of tokens to transfer
     * @return bool True if the transfer was successful
     */
    function forcedTransfer(address from, address to, uint256 amount) external returns (bool);

    // Enhanced Force Transfer Functions
    // Note: Enhanced force transfer functions are implemented in SecurityToken contract
    // but not included in interface to avoid enum type compilation issues

    /**
     * @dev Update the identity registry/whitelist contract address
     * @param newIdentityRegistry The address of the new identity registry
     */
    function updateIdentityRegistry(address newIdentityRegistry) external;

    /**
     * @dev Update token metadata
     * @param tokenPrice_ New token price metadata
     * @param bonusTiers_ New bonus tiers metadata
     * @param tokenDetails_ New token details metadata
     */
    function updateTokenMetadata(string memory tokenPrice_, string memory bonusTiers_, string memory tokenDetails_) external;

    /**
     * @dev Update max supply
     * @param newMaxSupply New maximum supply
     */
    function updateMaxSupply(uint256 newMaxSupply) external;

    /**
     * @dev Returns the token price metadata
     * @return string The token price metadata
     */
    function tokenPrice() external view returns (string memory);

    /**
     * @dev Returns the bonus tiers metadata
     * @return string The bonus tiers metadata
     */
    function bonusTiers() external view returns (string memory);

    /**
     * @dev Returns the token details metadata
     * @return string The token details metadata
     */
    function tokenDetails() external view returns (string memory);

    /**
     * @dev Returns the maximum supply of the token
     * @return uint256 The maximum supply
     */
    function maxSupply() external view returns (uint256);

    /**
     * @dev Returns the identity registry/whitelist contract address
     * @return address The identity registry contract address
     */
    function identityRegistry() external view returns (address);

    /**
     * @dev Returns the token version
     * @return string The token version
     */
    function version() external view returns (string memory);

    /**
     * @dev Check if a transfer is valid according to transfer rules and compliance
     * @param from Source address
     * @param to Destination address
     * @param amount Amount of tokens to transfer
     * @return bool True if the transfer is valid
     */
    function canTransfer(address from, address to, uint256 amount) external view returns (bool);

    /**
     * @dev Add a new agent to the token
     * @param agent Address to add as an agent
     */
    function addAgent(address agent) external;

    /**
     * @dev Remove an agent from the token
     * @param agent Address to remove as an agent
     */
    function removeAgent(address agent) external;

    /**
     * @dev Get the total number of agents
     * @return uint256 Number of agents
     */
    function getAgentCount() external view returns (uint256);

    /**
     * @dev Get agent address by index
     * @param index Index in the agent list
     * @return address Agent address
     */
    function getAgentAt(uint256 index) external view returns (address);

    /**
     * @dev Add an address to the whitelist
     * @param account The address to add to the whitelist
     */
    function addToWhitelist(address account) external;

    /**
     * @dev Batch add addresses to the whitelist
     * @param accounts The addresses to add to the whitelist
     */
    function batchAddToWhitelist(address[] calldata accounts) external;

    /**
     * @dev Remove an address from the whitelist
     * @param account The address to remove from the whitelist
     */
    function removeFromWhitelist(address account) external;

    /**
     * @dev Check if an address is whitelisted
     * @param account The address to check
     * @return bool True if the address is whitelisted, false otherwise
     */
    function isWhitelisted(address account) external view returns (bool);

    /**
     * @dev Freeze an address
     * @param account The address to freeze
     */
    function freezeAddress(address account) external;

    /**
     * @dev Unfreeze an address
     * @param account The address to unfreeze
     */
    function unfreezeAddress(address account) external;

    // Advanced Transfer Control Functions

    /**
     * @dev Enable/disable conditional transfers (approval flow)
     * @param enabled Whether to enable conditional transfers
     */
    function setConditionalTransfers(bool enabled) external;

    /**
     * @dev Approve a specific transfer in the approval flow
     * @param from The address to transfer from
     * @param to The address to transfer to
     * @param amount The amount to transfer
     * @param nonce The nonce for this transfer
     */
    function approveTransfer(address from, address to, uint256 amount, uint256 nonce) external;

    /**
     * @dev Execute a pre-approved transfer
     * @param to The address to transfer to
     * @param amount The amount to transfer
     * @param nonce The nonce for this transfer
     */
    function executeApprovedTransfer(address to, uint256 amount, uint256 nonce) external;

    /**
     * @dev Enable/disable transfer whitelisting
     * @param enabled Whether to enable transfer whitelisting
     */
    function setTransferWhitelist(bool enabled) external;

    /**
     * @dev Add/remove an address from transfer whitelist
     * @param account The address to update
     * @param whitelisted Whether to whitelist the address
     */
    function setTransferWhitelistAddress(address account, bool whitelisted) external;

    /**
     * @dev Enable/disable transfer fees
     * @param enabled Whether to enable transfer fees
     * @param feePercentage Fee percentage in basis points (100 = 1%)
     * @param feeCollector Address to collect fees (must be whitelisted)
     */
    function setTransferFees(bool enabled, uint256 feePercentage, address feeCollector) external;

    /**
     * @dev Check if conditional transfers are enabled
     * @return bool True if conditional transfers are enabled
     */
    function conditionalTransfersEnabled() external view returns (bool);

    /**
     * @dev Check if transfer whitelisting is enabled
     * @return bool True if transfer whitelisting is enabled
     */
    function transferWhitelistEnabled() external view returns (bool);

    /**
     * @dev Check if an address is transfer whitelisted
     * @param account The address to check
     * @return bool True if the address is transfer whitelisted
     */
    function isTransferWhitelisted(address account) external view returns (bool);

    /**
     * @dev Check if transfer fees are enabled
     * @return bool True if transfer fees are enabled
     */
    function transferFeesEnabled() external view returns (bool);

    /**
     * @dev Get transfer fee configuration
     * @return feePercentage Fee percentage in basis points
     * @return feeCollector Address that collects fees
     */
    function getTransferFeeConfig() external view returns (uint256 feePercentage, address feeCollector);

    /**
     * @dev Get the next nonce for an address
     * @param account The address to get nonce for
     * @return uint256 The next nonce
     */
    function getTransferNonce(address account) external view returns (uint256);

    // Agreement Functions

    /**
     * @dev Accept the agreement for the calling address
     */
    function acceptAgreement() external;

    /**
     * @dev Check if an address has accepted the agreement
     * @param account The address to check
     * @return bool True if the address has accepted the agreement
     */
    function hasAcceptedAgreement(address account) external view returns (bool);

    /**
     * @dev Get the timestamp when an address accepted the agreement
     * @param account The address to check
     * @return uint256 The timestamp when the agreement was accepted (0 if not accepted)
     */
    function getAgreementAcceptanceTimestamp(address account) external view returns (uint256);

    // Enhanced Force Transfer View Functions
    // Note: Enhanced force transfer view functions are implemented in SecurityToken contract
}