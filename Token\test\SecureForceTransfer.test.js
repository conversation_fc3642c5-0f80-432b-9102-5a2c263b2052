const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("Secure Force Transfer Tests", function () {
    let deployer, user1, user2, user3, agent, authorizer1, authorizer2;
    let claimRegistry, identityRegistry, compliance, securityToken;
    
    const TOKEN_NAME = "Test Security Token";
    const TOKEN_SYMBOL = "TST";
    const TOKEN_DECIMALS = 0;
    const TOKEN_MAX_SUPPLY = ethers.parseUnits("1000000", TOKEN_DECIMALS);
    const USA_COUNTRY_CODE = 840;

    // Force Transfer Types
    const ForceTransferType = {
        EMERGENCY_SECURITY: 0,
        REGULATORY_ORDER: 1,
        CORPORATE_ACTION: 2,
        RECOVERY_ASSISTANCE: 3
    };

    beforeEach(async function () {
        [deployer, user1, user2, user3, agent, authorizer1, authorizer2] = await ethers.getSigners();

        // Deploy ClaimRegistry
        const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
        claimRegistry = await upgrades.deployProxy(
            ClaimRegistry,
            [deployer.address],
            { initializer: "initialize", kind: "uups" }
        );
        await claimRegistry.waitForDeployment();

        // Deploy IdentityRegistry
        const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
        identityRegistry = await upgrades.deployProxy(
            IdentityRegistry,
            [deployer.address, await claimRegistry.getAddress()],
            { initializer: "initialize", kind: "uups" }
        );
        await identityRegistry.waitForDeployment();

        // Deploy Compliance
        const Compliance = await ethers.getContractFactory("Compliance");
        compliance = await upgrades.deployProxy(
            Compliance,
            [deployer.address, await identityRegistry.getAddress()],
            { initializer: "initialize", kind: "uups" }
        );
        await compliance.waitForDeployment();

        // Deploy SecurityToken
        const SecurityToken = await ethers.getContractFactory("SecurityToken");
        securityToken = await upgrades.deployProxy(
            SecurityToken,
            [
                TOKEN_NAME,
                TOKEN_SYMBOL,
                TOKEN_DECIMALS,
                TOKEN_MAX_SUPPLY,
                await identityRegistry.getAddress(),
                await compliance.getAddress(),
                deployer.address,
                "10 USD",
                "Tier 1: 5%",
                "Test token details",
                ""
            ],
            { initializer: "initialize", kind: "uups" }
        );
        await securityToken.waitForDeployment();

        // Grant roles
        await securityToken.grantRole(await securityToken.AGENT_ROLE(), agent.address);
        await securityToken.grantRole(await securityToken.FORCE_TRANSFER_AUTHORIZER_ROLE(), authorizer1.address);
        await securityToken.grantRole(await securityToken.FORCE_TRANSFER_AUTHORIZER_ROLE(), authorizer2.address);

        // Set up test users
        await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
        await identityRegistry.addToWhitelist(user1.address);
        await identityRegistry.approveKyc(user1.address);

        await identityRegistry.registerIdentity(user2.address, USA_COUNTRY_CODE);
        await identityRegistry.addToWhitelist(user2.address);
        await identityRegistry.approveKyc(user2.address);

        await identityRegistry.registerIdentity(user3.address, USA_COUNTRY_CODE);
        await identityRegistry.addToWhitelist(user3.address);
        await identityRegistry.approveKyc(user3.address);

        // Mint tokens to user1
        await securityToken.connect(agent).mint(user1.address, 1000);
    });

    describe("Legacy Force Transfer", function () {
        it("Should still work as before", async function () {
            // Freeze user1
            await identityRegistry.freezeAddress(user1.address);

            // Legacy force transfer should still work
            await securityToken.forcedTransfer(user1.address, user2.address, 500);

            expect(await securityToken.balanceOf(user1.address)).to.equal(500);
            expect(await securityToken.balanceOf(user2.address)).to.equal(500);
        });
    });

    describe("Secure Force Transfer System", function () {
        it("Should check initial configuration", async function () {
            // Check default signature requirements
            expect(await securityToken.getRequiredSignatures(ForceTransferType.EMERGENCY_SECURITY)).to.equal(1);
            expect(await securityToken.getRequiredSignatures(ForceTransferType.REGULATORY_ORDER)).to.equal(1);
            expect(await securityToken.getRequiredSignatures(ForceTransferType.CORPORATE_ACTION)).to.equal(2);
            expect(await securityToken.getRequiredSignatures(ForceTransferType.RECOVERY_ASSISTANCE)).to.equal(2);
        });

        it("Should initiate emergency security transfer", async function () {
            const transferHash = await securityToken.initiateSecureForceTransfer(
                user1.address,
                user2.address,
                300,
                ForceTransferType.EMERGENCY_SECURITY,
                "Security emergency - compromised account"
            );

            // Should emit event
            await expect(transferHash).to.emit(securityToken, "ForceTransferInitiated");
        });

        it("Should require authorization for force transfers", async function () {
            const tx = await securityToken.initiateSecureForceTransfer(
                user1.address,
                user2.address,
                300,
                ForceTransferType.EMERGENCY_SECURITY,
                "Security emergency"
            );
            
            const receipt = await tx.wait();
            const event = receipt.logs.find(log => {
                try {
                    return securityToken.interface.parseLog(log).name === "ForceTransferInitiated";
                } catch {
                    return false;
                }
            });
            
            const transferHash = event ? securityToken.interface.parseLog(event).args.transferHash : null;
            expect(transferHash).to.not.be.null;

            // Should not be authorized initially
            expect(await securityToken.isForceTransferAuthorized(transferHash)).to.be.false;

            // Authorize the transfer
            await securityToken.connect(authorizer1).authorizeForceTransfer(
                transferHash,
                ForceTransferType.EMERGENCY_SECURITY
            );

            // Should be authorized now (emergency only needs 1 signature)
            expect(await securityToken.isForceTransferAuthorized(transferHash)).to.be.true;
        });
    });

    describe("Graduated Compliance", function () {
        it("Should handle emergency transfers from frozen addresses", async function () {
            // Freeze user1
            await identityRegistry.freezeAddress(user1.address);

            const tx = await securityToken.initiateSecureForceTransfer(
                user1.address,
                user2.address,
                300,
                ForceTransferType.EMERGENCY_SECURITY,
                "Emergency security transfer"
            );

            const receipt = await tx.wait();
            const event = receipt.logs.find(log => {
                try {
                    return securityToken.interface.parseLog(log).name === "ForceTransferInitiated";
                } catch {
                    return false;
                }
            });
            
            const transferHash = securityToken.interface.parseLog(event).args.transferHash;

            // Authorize and execute
            await securityToken.connect(authorizer1).authorizeForceTransfer(
                transferHash,
                ForceTransferType.EMERGENCY_SECURITY
            );

            await securityToken.executeSecureForceTransfer(
                transferHash,
                user1.address,
                user2.address,
                300,
                ForceTransferType.EMERGENCY_SECURITY,
                "Emergency security transfer"
            );

            expect(await securityToken.balanceOf(user1.address)).to.equal(700);
            expect(await securityToken.balanceOf(user2.address)).to.equal(300);
        });

        it("Should require multiple signatures for corporate actions", async function () {
            const tx = await securityToken.initiateSecureForceTransfer(
                user1.address,
                user2.address,
                300,
                ForceTransferType.CORPORATE_ACTION,
                "Corporate restructuring"
            );

            const receipt = await tx.wait();
            const event = receipt.logs.find(log => {
                try {
                    return securityToken.interface.parseLog(log).name === "ForceTransferInitiated";
                } catch {
                    return false;
                }
            });
            
            const transferHash = securityToken.interface.parseLog(event).args.transferHash;

            // One signature should not be enough
            await securityToken.connect(authorizer1).authorizeForceTransfer(
                transferHash,
                ForceTransferType.CORPORATE_ACTION
            );

            expect(await securityToken.isForceTransferAuthorized(transferHash)).to.be.false;
            expect(await securityToken.getForceTransferSignatureCount(transferHash)).to.equal(1);

            // Second signature should authorize it
            await securityToken.connect(authorizer2).authorizeForceTransfer(
                transferHash,
                ForceTransferType.CORPORATE_ACTION
            );

            expect(await securityToken.isForceTransferAuthorized(transferHash)).to.be.true;
        });
    });

    describe("Security Features", function () {
        it("Should prevent unauthorized execution", async function () {
            const transferHash = ethers.keccak256(ethers.toUtf8Bytes("fake_hash"));

            await expect(
                securityToken.executeSecureForceTransfer(
                    transferHash,
                    user1.address,
                    user2.address,
                    300,
                    ForceTransferType.EMERGENCY_SECURITY,
                    "Unauthorized attempt"
                )
            ).to.be.revertedWith("SecurityToken: transfer not authorized");
        });

        it("Should enforce time delays for non-emergency transfers", async function () {
            const tx = await securityToken.initiateSecureForceTransfer(
                user1.address,
                user2.address,
                300,
                ForceTransferType.CORPORATE_ACTION,
                "Corporate action with delay"
            );

            const receipt = await tx.wait();
            const event = receipt.logs.find(log => {
                try {
                    return securityToken.interface.parseLog(log).name === "ForceTransferInitiated";
                } catch {
                    return false;
                }
            });
            
            const transferHash = securityToken.interface.parseLog(event).args.transferHash;

            // Authorize the transfer
            await securityToken.connect(authorizer1).authorizeForceTransfer(
                transferHash,
                ForceTransferType.CORPORATE_ACTION
            );
            await securityToken.connect(authorizer2).authorizeForceTransfer(
                transferHash,
                ForceTransferType.CORPORATE_ACTION
            );

            // Should fail due to time delay
            await expect(
                securityToken.executeSecureForceTransfer(
                    transferHash,
                    user1.address,
                    user2.address,
                    300,
                    ForceTransferType.CORPORATE_ACTION,
                    "Corporate action with delay"
                )
            ).to.be.revertedWith("SecurityToken: transfer delay not met");

            // Fast forward time
            await time.increase(24 * 60 * 60 + 1); // 24 hours + 1 second

            // Should work now
            await securityToken.executeSecureForceTransfer(
                transferHash,
                user1.address,
                user2.address,
                300,
                ForceTransferType.CORPORATE_ACTION,
                "Corporate action with delay"
            );

            expect(await securityToken.balanceOf(user1.address)).to.equal(700);
            expect(await securityToken.balanceOf(user2.address)).to.equal(300);
        });
    });
});
