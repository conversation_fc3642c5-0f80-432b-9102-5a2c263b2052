'use client';

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';
import Link from 'next/link';
import { getContractAddresses, getNetworkConfig, getKnownTokens, isKnownToken } from '../config';
import SecurityTokenFactoryABI from '../contracts/SecurityTokenFactory.json';
import SecurityTokenABI from '../contracts/SecurityToken.json';

interface Token {
  address: string;
  name: string;
  symbol: string;
  maxSupply?: string;
  totalSupply?: string;
  whitelistAddress: string;
  admin?: string;
  deployedAt?: number; // Block timestamp
  tokenImageUrl?: string;
}

export default function Home() {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [network, setNetwork] = useState('amoy');
  const [factoryAddress, setFactoryAddress] = useState<string>('');
  const [tokenImplementation, setTokenImplementation] = useState<string>('');
  const [whitelistImplementation, setWhitelistImplementation] = useState<string>('');
  const [verificationAddress, setVerificationAddress] = useState<string>('');
  const [walletConnected, setWalletConnected] = useState(false);
  const [loadMethod, setLoadMethod] = useState<'logs' | 'direct'>('logs');

  useEffect(() => {
    // Get contract addresses for current network
    const addresses = getContractAddresses(network);
    setFactoryAddress(addresses.factory || '');
    setTokenImplementation(addresses.tokenImplementation || '');
    setWhitelistImplementation(addresses.whitelistImplementation || '');

    checkWalletConnection();
  }, [network]);

  useEffect(() => {
    if (walletConnected) {
      fetchTokens();
    } else {
      // Load known tokens even without wallet connection for display
      loadKnownTokensOnly();
    }
  }, [walletConnected, network]);

  // Function to load only known tokens (for display without wallet)
  const loadKnownTokensOnly = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const knownTokens = getKnownTokens(network);

      if (knownTokens.length > 0) {
        console.log(`Loading ${knownTokens.length} known tokens for display...`);

        // Create basic token objects from known tokens
        const basicTokens = knownTokens.map(knownToken => ({
          address: knownToken.address,
          name: knownToken.name,
          symbol: knownToken.symbol,
          maxSupply: "Connect wallet to view",
          totalSupply: "Connect wallet to view",
          whitelistAddress: ethers.ZeroAddress,
          tokenImageUrl: ""
        }));

        setTokens(basicTokens);
        setError("Connect your wallet to view live token data and access management features.");
      } else {
        setTokens([]);
        setError("No known tokens configured for this network. Connect your wallet to discover tokens from the factory.");
      }
    } catch (error) {
      console.error("Error loading known tokens:", error);
      setError("Error loading token information. Please try again.");
    }

    setIsLoading(false);
  };

  const checkWalletConnection = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        const accounts = await provider.listAccounts();

        if (accounts.length > 0) {
          setWalletConnected(true);
        } else {
          setWalletConnected(false);
        }
      }
    } catch (error) {
      console.error("Error checking wallet connection:", error);
      setWalletConnected(false);
    }
  };

  const connectWallet = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send("eth_requestAccounts", []);
        setWalletConnected(true);
      } else {
        setError("Please install MetaMask to use this feature!");
      }
    } catch (error) {
      console.error("Error connecting wallet:", error);
    }
  };

  // Add your token address to verify
  const addTokenManually = () => {
    if (!verificationAddress) {
      alert('Please enter a token address');
      return;
    }

    if (!ethers.isAddress(verificationAddress)) {
      alert('Please enter a valid address');
      return;
    }

    loadTokenByAddress(verificationAddress);
  };

  const loadTokenByAddress = async (address: string) => {
    try {
      setIsLoading(true);

      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      // Use the new loadTokenDetails function
      const tokenDetails = await loadTokenDetails(address);

      if (tokenDetails) {
        // Check if the token is already in the list
        const exists = tokens.some(token => token.address.toLowerCase() === address.toLowerCase());

        if (!exists) {
          setTokens(prevTokens => [...prevTokens, tokenDetails]);
        }

        setVerificationAddress('');
      } else {
        alert("Could not load token details. Is this a valid Security Token address?");
      }

      setIsLoading(false);
    } catch (err: any) {
      console.error('Error loading token:', err);
      setError(err.message || 'Error loading token. Please try again.');
      setIsLoading(false);
    }
  };

  const loadTokenDetails = async (address: string): Promise<Token | null> => {
    try {
      if (!window.ethereum) {
        throw new Error('MetaMask not available');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);

      // Get the token contract
      const tokenContract = new ethers.Contract(
        address,
        SecurityTokenABI.abi,
        provider
      );

      // Read token details
      const name = await tokenContract.name();
      const symbol = await tokenContract.symbol();

      // Get decimals to format amounts correctly
      const decimalsRaw = await tokenContract.decimals();
      const decimals = Number(decimalsRaw);

      let maxSupply = "0";
      let totalSupply = "0";
      let whitelistAddress = "******************************************";

      try {
        const maxSupplyRaw = await tokenContract.maxSupply();
        maxSupply = decimals === 0
          ? maxSupplyRaw.toString()
          : ethers.formatUnits(maxSupplyRaw, decimals);
      } catch (err) {
        console.warn("Could not read maxSupply:", err);
      }

      try {
        const totalSupplyRaw = await tokenContract.totalSupply();
        totalSupply = decimals === 0
          ? totalSupplyRaw.toString()
          : ethers.formatUnits(totalSupplyRaw, decimals);
      } catch (err) {
        console.warn("Could not read totalSupply:", err);
      }

      try {
        whitelistAddress = await tokenContract.identityRegistry();
      } catch (err) {
        try {
          whitelistAddress = await tokenContract.whitelistAddress();
        } catch (err2) {
          console.warn("Could not read whitelist address:", err2);
        }
      }

      // Try to get token image URL if supported
      let tokenImageUrl = "";
      try {
        tokenImageUrl = await tokenContract.tokenImageUrl();
      } catch (err) {
        console.log("Token doesn't support image URL or image URL is empty");
      }

      return {
        address,
        name,
        symbol,
        maxSupply,
        totalSupply,
        whitelistAddress,
        tokenImageUrl
      };
    } catch (error) {
      console.error(`Error loading token details for ${address}:`, error);
      return null;
    }
  };

  const fetchTokens = async () => {
    setIsLoading(true);
    setError(null);
    setTokens([]);

    try {
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const provider = new ethers.BrowserProvider(window.ethereum);
      const networkConfig = getNetworkConfig(network);
      const contractAddresses = getContractAddresses(network);

      if (!contractAddresses.factory) {
        throw new Error(`No factory address configured for network: ${network}`);
      }

      // Connect to the factory contract
      const factory = new ethers.Contract(
        contractAddresses.factory,
        SecurityTokenFactoryABI.abi,
        provider
      );

      console.log("Loading tokens from factory:", contractAddresses.factory);
      console.log("Network:", network);
      console.log("Provider:", provider);

      try {
        // Try to get tokens using the new enumeration methods
        console.log("Attempting to fetch tokens using factory enumeration...");

        try {
          // First, try to get the token count (new factory enumeration)
          const tokenCount = await factory.getTokenCount();
          console.log(`Factory reports ${tokenCount} deployed tokens`);

          if (tokenCount > 0) {
            // Get all token addresses at once for better performance
            const tokenAddresses = await factory.getAllDeployedTokens();
            console.log("Retrieved token addresses:", tokenAddresses);

            // Load details for each token
            const tokenPromises = tokenAddresses.map(async (address: string) => {
              try {
                return await loadTokenDetails(address);
              } catch (error) {
                console.warn(`Failed to load token details for ${address}:`, error);
                return null;
              }
            });

            const tokenResults = await Promise.all(tokenPromises);
            const validTokens = tokenResults.filter(token => token !== null);

            if (validTokens.length > 0) {
              setTokens(validTokens);
              console.log(`Successfully loaded ${validTokens.length} tokens from factory`);
            } else {
              setError("Factory has tokens but could not load their details. Please check network connection.");
            }
          } else {
            setError("No tokens found in factory. Create your first token to see it here.");
          }
        } catch (enumerationError) {
          console.warn("Factory enumeration failed, trying event-based discovery:", enumerationError);

          // Fallback: Use event-based token discovery for older factories
          try {
            console.log("Searching for TokenDeployed events...");

            // Get TokenDeployed events from the factory
            const filter = factory.filters.TokenDeployed();
            let events = [];

            // Try different block ranges to find events
            const ranges = [-10000, -50000]; // Last 10k, then 50k blocks

            for (const range of ranges) {
              try {
                events = await factory.queryFilter(filter, range);
                if (events.length > 0) break; // Found events, stop searching
              } catch (error) {
                console.warn(`Failed to query ${Math.abs(range)} blocks:`, error);
              }
            }

            console.log(`Found ${events.length} TokenDeployed events`);

            if (events.length > 0) {
              // Extract unique token addresses from events
              const tokenAddresses = [...new Set(events.map(event => event.args?.tokenAddress).filter(Boolean))];
              console.log("Token addresses from events:", tokenAddresses);

              // Load details for each token
              const tokenPromises = tokenAddresses.map(async (address: string) => {
                try {
                  return await loadTokenDetails(address);
                } catch (error) {
                  console.warn(`Failed to load token details for ${address}:`, error);
                  return null;
                }
              });

              const tokenResults = await Promise.all(tokenPromises);
              const validTokens = tokenResults.filter(token => token !== null);

              if (validTokens.length > 0) {
                setTokens(validTokens);
                console.log(`Successfully loaded ${validTokens.length} tokens from events`);
              } else {
                setError("Found token events but could not load token details. Please check network connection.");
              }
            } else {
              setError("No tokens found in factory events. Create your first token to see it here.");
            }
          } catch (eventError) {
            console.warn("Event-based discovery failed:", eventError);

            // Final fallback: Load known tokens from configuration
            console.log("Falling back to known tokens from configuration...");
            const knownTokens = getKnownTokens(network);

            if (knownTokens.length > 0) {
              console.log(`Loading ${knownTokens.length} known tokens...`);

              const knownTokenPromises = knownTokens.map(async (knownToken) => {
                try {
                  const tokenDetails = await loadTokenDetails(knownToken.address);
                  return tokenDetails;
                } catch (error) {
                  console.warn(`Failed to load known token ${knownToken.address}:`, error);
                  // Return basic info even if contract call fails
                  return {
                    address: knownToken.address,
                    name: knownToken.name,
                    symbol: knownToken.symbol,
                    maxSupply: "Unknown",
                    totalSupply: "Unknown",
                    whitelistAddress: ethers.ZeroAddress,
                    tokenImageUrl: ""
                  };
                }
              });

              const knownTokenResults = await Promise.all(knownTokenPromises);
              const validKnownTokens = knownTokenResults.filter(token => token !== null);

              if (validKnownTokens.length > 0) {
                setTokens(validKnownTokens);
                console.log(`Successfully loaded ${validKnownTokens.length} known tokens`);
                setError("Factory enumeration failed, but loaded known tokens. You can add more tokens manually below.");
              } else {
                setError("Could not load tokens from factory or known tokens. Please add token addresses manually using the tool below.");
              }
            } else {
              setError("Could not load tokens from factory. Please add token addresses manually using the tool below.");
            }
          }
        }
      } catch (err: unknown) {
        console.error("Error loading tokens:", err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error";

        // Try to load known tokens as fallback even on factory connection error
        console.log("Factory connection failed, trying known tokens...");
        const knownTokens = getKnownTokens(network);

        if (knownTokens.length > 0) {
          try {
            const knownTokenPromises = knownTokens.map(async (knownToken) => {
              try {
                const tokenDetails = await loadTokenDetails(knownToken.address);
                return tokenDetails;
              } catch (error) {
                console.warn(`Failed to load known token ${knownToken.address}:`, error);
                return {
                  address: knownToken.address,
                  name: knownToken.name,
                  symbol: knownToken.symbol,
                  maxSupply: "Unknown",
                  totalSupply: "Unknown",
                  whitelistAddress: ethers.ZeroAddress,
                  tokenImageUrl: ""
                };
              }
            });

            const knownTokenResults = await Promise.all(knownTokenPromises);
            const validKnownTokens = knownTokenResults.filter(token => token !== null);

            if (validKnownTokens.length > 0) {
              setTokens(validKnownTokens);
              console.log(`Loaded ${validKnownTokens.length} known tokens as fallback`);
              setError("Factory connection failed, but loaded known tokens. Please check your network connection.");
            } else {
              setError("Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.");
            }
          } catch (fallbackError) {
            console.error("Fallback token loading failed:", fallbackError);
            setError("Could not load tokens from factory or known tokens. Please add token addresses manually using the verify tool below.");
          }
        } else {
          setError("Could not load tokens from factory. Please add token addresses manually using the verify tool below.");
        }
      }

      setIsLoading(false);
    } catch (err: any) {
      console.error('Error fetching tokens:', err);
      setError(err.message || 'Error fetching tokens. Please try again.');
      setIsLoading(false);
    }
  };

  const handleNetworkChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newNetwork = event.target.value;
    console.log(`Switching to network: ${newNetwork}`);
    setNetwork(newNetwork);
  };

  const verifyToken = () => {
    if (!verificationAddress) {
      alert('Please enter a token address to verify');
      return;
    }

    let explorerUrl = '';
    if (network === 'amoy') {
      explorerUrl = `https://www.oklink.com/amoy/address/${verificationAddress}`;
    } else if (network === 'polygon') {
      explorerUrl = `https://polygonscan.com/address/${verificationAddress}`;
    }

    if (explorerUrl) {
      window.open(explorerUrl, '_blank');
    }
  };

  const getBlockExplorerUrl = (network: string) => {
    if (network === 'amoy') {
      return 'https://www.oklink.com/amoy';
    } else if (network === 'polygon') {
      return 'https://polygonscan.com';
    }
    return '#';
  };

  const viewFactoryTokens = () => {
    let explorerUrl = '';
    if (network === 'amoy') {
      explorerUrl = `https://www.oklink.com/amoy/address/${factoryAddress}#eventlog`;
    } else if (network === 'polygon') {
      explorerUrl = `https://polygonscan.com/address/${factoryAddress}#events`;
    }

    if (explorerUrl) {
      window.open(explorerUrl, '_blank');
    }
  };

  const formatNumber = (value: string) => {
    // Format the number with commas
    const number = parseFloat(value);
    return number.toLocaleString(undefined, { maximumFractionDigits: 0 });
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Security Token Dashboard</h1>
        <p className="text-gray-600">
          View and manage your security tokens
        </p>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <div>
          <label htmlFor="network" className="block text-sm font-medium text-gray-700 mb-1">
            Network
          </label>
          <select
            id="network"
            className="p-2 border border-gray-300 rounded-md"
            value={network}
            onChange={handleNetworkChange}
          >
            <option value="amoy">Amoy Testnet</option>
            <option value="polygon">Polygon Mainnet</option>
          </select>
        </div>

        <div className="flex space-x-3">
          {!walletConnected && (
            <button
              onClick={connectWallet}
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition"
            >
              Connect Wallet
            </button>
          )}
          <Link
            href="/modular-tokens"
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition"
          >
            Create New Token
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
          <p>{error}</p>
        </div>
      )}

      <div className="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 mb-6">
        <h3 className="font-bold mb-2">Connected to Your Deployed Factory Contract</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
          <div>
            <p className="text-sm font-semibold">Factory Address:</p>
            <p className="font-mono text-sm">{factoryAddress}</p>
          </div>

          {tokenImplementation && (
            <div>
              <p className="text-sm font-semibold">Token Implementation:</p>
              <p className="font-mono text-sm">{tokenImplementation}</p>
            </div>
          )}

          {whitelistImplementation && (
            <div>
              <p className="text-sm font-semibold">Whitelist Implementation:</p>
              <p className="font-mono text-sm">{whitelistImplementation}</p>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-4 mt-3">
          <a
            href={`${getBlockExplorerUrl(network)}/address/${factoryAddress}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-green-700 underline hover:text-green-900"
          >
            View Factory on {network === 'amoy' ? 'OKLink Explorer' : 'PolygonScan'}
          </a>

          <button
            onClick={viewFactoryTokens}
            className="bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm"
          >
            View All Factory Token Deployments
          </button>
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">Quick Actions</h3>

        {/* Navigation Links */}
        <div className="flex flex-wrap gap-3 mb-6 p-4 bg-gray-50 rounded-lg">
          <Link
            href="/modular-tokens"
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm font-medium"
          >
            🚀 Create Token
          </Link>
          <Link
            href="/modular-tokens"
            className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm font-medium"
          >
            🔒 Manage Tokens
          </Link>
          <Link
            href="/clients"
            className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm font-medium"
          >
            👥 Client Management
          </Link>
          <button
            onClick={() => window.open('https://amoy.polygonscan.com/', '_blank')}
            className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded text-sm font-medium"
          >
            🔍 Block Explorer
          </button>
        </div>

        <h4 className="text-lg font-medium mb-2">Add Token to Dashboard</h4>
        <p className="mb-4 text-gray-600">
          Manually add a token by entering its address below.
        </p>
        <div className="flex space-x-2">
          <input
            type="text"
            value={verificationAddress}
            onChange={(e) => setVerificationAddress(e.target.value)}
            placeholder="Enter token address (0x...)"
            className="flex-grow p-2 border border-gray-300 rounded"
          />
          <button
            onClick={addTokenManually}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          >
            Add Token
          </button>
          <button
            onClick={verifyToken}
            className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded"
          >
            Verify on Explorer
          </button>
        </div>
        <p className="mt-2 text-xs text-gray-500">
          Note: After creating a token, you can add it here to display it on the dashboard.
        </p>
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
        <div className="p-4 border-b">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-xl font-semibold">Security Token Dashboard</h3>
              {walletConnected ? (
                <p className="text-sm text-gray-600 mt-1">
                  Live data from {network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet'} factory
                </p>
              ) : (
                <p className="text-sm text-gray-600 mt-1">
                  Showing known tokens - connect wallet for live data
                </p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${walletConnected ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span className="text-sm text-gray-600">
                {walletConnected ? 'Connected' : 'Offline Mode'}
              </span>
            </div>
          </div>

          <div className="flex space-x-2 mt-3">
            <button
              onClick={walletConnected ? fetchTokens : loadKnownTokensOnly}
              className="text-sm bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded"
              disabled={isLoading}
            >
              {isLoading ? 'Loading...' : 'Refresh Token List'}
            </button>
            {!walletConnected && (
              <button
                onClick={connectWallet}
                className="text-sm bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded"
              >
                Connect for Live Data
              </button>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center my-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : tokens.length > 0 ? (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Symbol
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Supply
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Max Supply
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {tokens.map((token, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {/* Token Image */}
                      <div className="flex-shrink-0 h-10 w-10">
                        {token.tokenImageUrl ? (
                          <img
                            className="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                            src={token.tokenImageUrl}
                            alt={`${token.name} logo`}
                            onError={(e) => {
                              // Fallback to default icon if image fails to load
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        {/* Default icon fallback */}
                        <div className={`h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center ${token.tokenImageUrl ? 'hidden' : ''}`}>
                          <span className="text-sm font-medium text-gray-700">
                            {token.symbol.substring(0, 2).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{token.name}</div>
                        <div className="text-xs text-gray-500 truncate" title={token.address}>
                          {token.address.substring(0, 10)}...{token.address.substring(token.address.length - 8)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {token.symbol}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {token.totalSupply ? formatNumber(token.totalSupply) : "—"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {token.maxSupply ? formatNumber(token.maxSupply) : "—"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      walletConnected && token.totalSupply !== "Connect wallet to view"
                        ? isKnownToken(network, token.address)
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {walletConnected && token.totalSupply !== "Connect wallet to view"
                        ? isKnownToken(network, token.address)
                          ? 'Known + Live'
                          : 'Factory'
                        : 'Known Token'
                      }
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Link
                      href={`/tokens/${token.address}`}
                      className="text-blue-600 hover:text-blue-900 mr-4"
                    >
                      View Details
                    </Link>
                    <a
                      href={getBlockExplorerUrl(network) + '/address/' + token.address}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-gray-900"
                    >
                      Explorer
                    </a>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="p-6 text-center">
            {walletConnected ? (
              <>
                <p className="text-gray-600 mb-4">No tokens found on {network} network.</p>
                <p className="text-gray-600 mb-4">
                  To add a token you've created, use the "Add Token to Dashboard" section above.
                </p>
                <Link
                  href="/modular-tokens"
                  className="text-blue-600 hover:text-blue-900 font-medium"
                >
                  Create your first token
                </Link>
              </>
            ) : (
              <button
                onClick={connectWallet}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
              >
                Connect Wallet to View Tokens
              </button>
            )}
          </div>
        )}
      </div>

      <div className="bg-white shadow-md rounded-lg p-6 text-center">
        <Link
          href="/modular-tokens"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded inline-block"
        >
          Create New Token on {network === 'amoy' ? 'Amoy Testnet' : 'Polygon Mainnet'}
        </Link>
      </div>
    </div>
  );
}
