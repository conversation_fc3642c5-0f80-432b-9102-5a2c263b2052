const { ethers } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
    console.log("🚀 Deploying Enhanced Token using Existing Factory...");
    
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

    // Use existing factory from amoy-final.json
    const existingDeployment = JSON.parse(
        fs.readFileSync(path.join(__dirname, '../deployments/amoy-final.json'), 'utf8')
    );
    
    const factoryAddress = existingDeployment.factoryAddress;
    console.log("Using existing factory at:", factoryAddress);

    try {
        // Connect to existing factory
        const factory = await ethers.getContractAt("SecurityTokenFactory", factoryAddress);
        
        // Check if we have deployer role
        const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
        const hasRole = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
        
        if (!hasRole) {
            console.log("⚠️ Granting deployer role...");
            await factory.grantRole(DEPLOYER_ROLE, deployer.address);
            console.log("✅ Deployer role granted");
        } else {
            console.log("✅ Already has deployer role");
        }

        // Deploy enhanced token
        console.log("\n🪙 Deploying Enhanced Security Token...");
        const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
        const tokenParams = {
            name: `Enhanced Security Token ${timestamp}`,
            symbol: `EST${timestamp}`,
            decimals: 0,
            maxSupply: ethers.parseUnits("1000000", 0),
            tokenPrice: "2.00 USD",
            bonusTiers: "Early: 20%, Standard: 15%",
            tokenDetails: "Security token with graduated force transfer compliance and multi-signature authorization",
            tokenImageUrl: ""
        };

        console.log(`Token Name: ${tokenParams.name}`);
        console.log(`Token Symbol: ${tokenParams.symbol}`);

        const deployTx = await factory.deploySecurityToken(
            tokenParams.name,
            tokenParams.symbol,
            tokenParams.decimals,
            tokenParams.maxSupply,
            deployer.address, // admin
            tokenParams.tokenPrice,
            tokenParams.bonusTiers,
            tokenParams.tokenDetails,
            tokenParams.tokenImageUrl
        );

        console.log("⏳ Waiting for deployment transaction...");
        const receipt = await deployTx.wait();
        console.log("✅ Transaction confirmed");
        console.log("Transaction hash:", receipt.hash);

        // Debug: Log all events
        console.log("📋 All events in transaction:");
        for (let i = 0; i < receipt.logs.length; i++) {
            try {
                const parsed = factory.interface.parseLog(receipt.logs[i]);
                console.log(`   Event ${i}: ${parsed.name}`);
                if (parsed.name === "TokenDeployed") {
                    console.log(`   Token Address: ${parsed.args.tokenAddress}`);
                }
            } catch (error) {
                console.log(`   Event ${i}: Unable to parse (${error.message})`);
            }
        }

        // Find the TokenDeployed event
        let tokenAddress, identityRegistryAddress, complianceAddress;

        const tokenDeployedEvent = receipt.logs.find(log => {
            try {
                const parsed = factory.interface.parseLog(log);
                return parsed.name === "TokenDeployed";
            } catch {
                return false;
            }
        });

        if (tokenDeployedEvent) {
            const parsedEvent = factory.interface.parseLog(tokenDeployedEvent);
            tokenAddress = parsedEvent.args.tokenAddress;
            identityRegistryAddress = parsedEvent.args.identityRegistryAddress;
            complianceAddress = parsedEvent.args.complianceAddress;
        } else {
            // Try to get the deployed tokens from the factory
            console.log("⚠️ TokenDeployed event not found, trying to get latest deployed token...");
            const deployedTokens = await factory.getAllDeployedTokens();
            if (deployedTokens.length > 0) {
                tokenAddress = deployedTokens[deployedTokens.length - 1];
                console.log("✅ Found latest deployed token:", tokenAddress);

                // Get the token contract to find associated contracts
                const token = await ethers.getContractAt("SecurityToken", tokenAddress);
                try {
                    identityRegistryAddress = await token.identityRegistry();
                    complianceAddress = await token.compliance();
                    console.log("✅ Identity Registry:", identityRegistryAddress);
                    console.log("✅ Compliance:", complianceAddress);
                } catch (error) {
                    console.log("⚠️ Could not get associated contract addresses:", error.message);
                }
            } else {
                throw new Error("No deployed tokens found and TokenDeployed event not found");
            }
        }
        
        console.log("✅ Enhanced Security Token deployed to:", tokenAddress);
        console.log("✅ Identity Registry deployed to:", identityRegistryAddress);
        console.log("✅ Compliance deployed to:", complianceAddress);

        // Test the enhanced functionality
        console.log("\n🧪 Testing enhanced force transfer functionality...");
        const token = await ethers.getContractAt("SecurityToken", tokenAddress);
        const identityRegistry = await ethers.getContractAt("IdentityRegistry", identityRegistryAddress);

        // Check if the token has the new enhanced features
        try {
            const FORCE_TRANSFER_AUTHORIZER_ROLE = await token.FORCE_TRANSFER_AUTHORIZER_ROLE();
            const hasRole = await token.hasRole(FORCE_TRANSFER_AUTHORIZER_ROLE, deployer.address);
            console.log("✅ Token has FORCE_TRANSFER_AUTHORIZER_ROLE:", hasRole);

            // Check force transfer configuration
            const ForceTransferType = {
                EMERGENCY_SECURITY: 0,
                REGULATORY_ORDER: 1,
                CORPORATE_ACTION: 2,
                RECOVERY_ASSISTANCE: 3
            };

            console.log("📋 Force Transfer Configuration:");
            for (const [typeName, typeValue] of Object.entries(ForceTransferType)) {
                const requiredSigs = await token.getRequiredSignatures(typeValue);
                console.log(`   ${typeName}: ${requiredSigs} signature(s) required`);
            }

            // Set up test scenario
            await identityRegistry.registerIdentity(deployer.address, 840); // USA
            await identityRegistry.addToWhitelist(deployer.address);
            await identityRegistry.approveKyc(deployer.address);

            const testRecipient = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6";
            await identityRegistry.registerIdentity(testRecipient, 840);
            await identityRegistry.addToWhitelist(testRecipient);
            await identityRegistry.approveKyc(testRecipient);

            // Mint tokens
            await token.mint(deployer.address, 1000);
            console.log("✅ Minted 1000 tokens to deployer");

            // Test legacy force transfer still works
            await token.forcedTransfer(deployer.address, testRecipient, 100);
            console.log("✅ Legacy force transfer works");

            // Test new secure force transfer initiation
            const transferHash = await token.initiateSecureForceTransfer.staticCall(
                deployer.address,
                testRecipient,
                50,
                ForceTransferType.EMERGENCY_SECURITY,
                "Test emergency transfer"
            );
            console.log("✅ Secure force transfer can be initiated");
            console.log("✅ All enhanced features are working!");

        } catch (error) {
            console.log("⚠️ Enhanced features not available - this is using the old SecurityToken implementation");
            console.log("   The token was deployed successfully but without the new force transfer features");
            console.log("   Error:", error.message);
        }

        // Save deployment information
        const deploymentInfo = {
            network: "amoy",
            chainId: "80002",
            timestamp: new Date().toISOString(),
            deployer: deployer.address,
            factoryAddress: factoryAddress,
            contracts: {
                token: tokenAddress,
                identityRegistry: identityRegistryAddress,
                compliance: complianceAddress
            },
            tokenInfo: {
                ...tokenParams,
                maxSupply: tokenParams.maxSupply.toString(),
                address: tokenAddress
            },
            deploymentMethod: "existing-factory",
            enhancedFeatures: false, // Will be true if enhanced features are detected
            notes: "Deployed using existing factory - may not have enhanced force transfer features"
        };

        // Try to detect if enhanced features are available
        try {
            const token = await ethers.getContractAt("SecurityToken", tokenAddress);
            await token.FORCE_TRANSFER_AUTHORIZER_ROLE();
            deploymentInfo.enhancedFeatures = true;
            deploymentInfo.notes = "Deployed with enhanced force transfer features";
        } catch {
            // Enhanced features not available
        }

        const deploymentPath = path.join(__dirname, '../deployments/amoy-enhanced-token.json');
        fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
        console.log(`\n💾 Deployment info saved to: ${deploymentPath}`);

        // Display summary
        console.log("\n🎉 TOKEN DEPLOYMENT COMPLETE!");
        console.log("=" .repeat(60));
        console.log("🌐 Network: Amoy Testnet");
        console.log("🏭 Factory:", factoryAddress);
        console.log("🪙 Token:", tokenAddress);
        console.log("🆔 Identity Registry:", identityRegistryAddress);
        console.log("📊 Compliance:", complianceAddress);
        console.log("=" .repeat(60));
        
        if (deploymentInfo.enhancedFeatures) {
            console.log("\n✨ Enhanced Features Available:");
            console.log("• Graduated force transfer compliance");
            console.log("• Multi-signature authorization system");
            console.log("• Time-delayed execution for non-emergency transfers");
            console.log("• Enhanced audit trail with detailed events");
            console.log("• Backward compatibility with legacy force transfers");
        } else {
            console.log("\n⚠️ Note: This token uses the standard SecurityToken implementation");
            console.log("   Enhanced force transfer features are not available");
            console.log("   To get enhanced features, you need to deploy a new factory");
            console.log("   with the updated SecurityToken implementation");
        }

        console.log("\n🔧 Next Steps:");
        console.log("1. Update admin panel to use new token address");
        console.log("2. Test token functionality in admin panel");
        console.log("3. Configure additional settings as needed");

        return {
            tokenAddress,
            identityRegistryAddress,
            complianceAddress,
            enhancedFeatures: deploymentInfo.enhancedFeatures
        };

    } catch (error) {
        console.error("❌ Deployment failed:", error);
        throw error;
    }
}

// Execute deployment
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;
