import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/prisma';
import { OrderStatus } from '@prisma/client';

// GET /api/tokens/[address]/orders - Get orders for a specific token
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  try {
    const { address } = await params;
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as OrderStatus | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Find the token by address
    const token = await prisma.token.findUnique({
      where: { address }
    });

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token not found' },
        { status: 404 }
      );
    }

    // Build where clause
    const where: any = { tokenId: token.id };
    if (status) where.status = status;

    // Get orders for this token
    const orders = await prisma.order.findMany({
      where,
      include: {
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
            tokenPrice: true,
            currency: true
          }
        },
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await prisma.order.count({ where });

    // Calculate summary statistics
    const summary = await prisma.order.groupBy({
      by: ['status'],
      where: { tokenId: token.id },
      _count: {
        status: true
      }
    });

    return NextResponse.json({
      success: true,
      orders,
      summary,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching token orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch token orders' },
      { status: 500 }
    );
  }
}

// POST /api/tokens/[address]/orders - Create a new order for a specific token
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  try {
    const { address } = await params;
    const body = await request.json();
    const {
      clientId,
      tokensOrdered,
      tokenPrice
    } = body;

    // Find the token by address
    const token = await prisma.token.findUnique({
      where: { address }
    });

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token not found' },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!clientId || !tokensOrdered) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate that client exists
    const client = await prisma.client.findUnique({
      where: { id: clientId }
    });

    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    // Use provided token price or fall back to token's default price
    const finalTokenPrice = tokenPrice || token.tokenPrice;

    // Calculate amount to pay
    const tokensOrderedNum = parseFloat(tokensOrdered);
    const tokenPriceNum = parseFloat(finalTokenPrice.replace(/[^\d.-]/g, '')); // Remove currency symbols
    const amountToPay = (tokensOrderedNum * tokenPriceNum).toString();

    // Generate payment reference
    const generatePaymentReference = (): string => {
      const prefix = 'ORD';
      const timestamp = Date.now().toString(36).toUpperCase();
      const random = Math.random().toString(36).substring(2, 8).toUpperCase();
      return `${prefix}-${timestamp}-${random}`;
    };

    const paymentReference = generatePaymentReference();

    // Create the order
    const order = await prisma.order.create({
      data: {
        tokenId: token.id,
        clientId,
        tokensOrdered: tokensOrdered.toString(),
        tokenPrice: finalTokenPrice,
        amountToPay,
        paymentReference,
        status: OrderStatus.CONFIRMED
      },
      include: {
        token: {
          select: {
            id: true,
            name: true,
            symbol: true,
            address: true,
            tokenPrice: true,
            currency: true
          }
        },
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      order
    });
  } catch (error) {
    console.error('Error creating token order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create token order' },
      { status: 500 }
    );
  }
}
