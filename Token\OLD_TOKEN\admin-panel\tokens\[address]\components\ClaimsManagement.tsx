'use client';

import { useState, useEffect } from 'react';

interface Claim {
  claimId: string;
  claimType: string;
  claimTypeId: number;
  issuer: string;
  issuedAt: string;
  expiresAt: string | null;
  revoked: boolean;
  valid: boolean;
  data: string;
  uri: string;
}

interface ClaimResponse {
  walletAddress: string;
  claims: Claim[];
  totalClaims: number;
  validClaims: number;
}

interface ClaimsManagementProps {
  tokenAddress: string;
}

export default function ClaimsManagement({ tokenAddress }: ClaimsManagementProps) {
  const [walletAddress, setWalletAddress] = useState('');
  const [claims, setClaims] = useState<Claim[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [issuingClaim, setIssuingClaim] = useState(false);
  const [tokenRequiredClaims, setTokenRequiredClaims] = useState<string[]>([]);
  const [loadingTokenClaims, setLoadingTokenClaims] = useState(true);
  const [newClaim, setNewClaim] = useState({
    claimType: 'KYC_CLAIM',
    data: '',
    uri: '',
    expiresAt: ''
  });

  // Tokeny-style claim types (using Tokeny-style Topic IDs)
  const claimTypes = [
    { value: 'KYC_CLAIM', label: 'KYC Verification', id: 10101010000001, description: 'Know Your Customer verification claim', tokenyFormat: true },
    { value: 'AML_CLAIM', label: 'AML Compliance', id: 10101010000002, description: 'Anti-Money Laundering verification claim', tokenyFormat: true },
    { value: 'IDENTITY_CLAIM', label: 'Identity Verification', id: 10101010000003, description: 'Identity document verification claim', tokenyFormat: true },
    { value: 'QUALIFICATION_CLAIM', label: 'Qualified Investor', id: 10101010000004, description: 'Accredited/qualified investor status', tokenyFormat: true },
    { value: 'ACCREDITATION_CLAIM', label: 'Professional Accreditation', id: 5, description: 'Professional accreditation claim', tokenyFormat: true },
    { value: 'RESIDENCE_CLAIM', label: 'Residence Proof', id: 6, description: 'Proof of residence verification', tokenyFormat: true },
    { value: 'TOKEN_ISSUER_CLAIM', label: 'Token Issuer', id: 7, description: 'Authorized token issuer claim', tokenyFormat: true }
  ];

  // Fetch token's required claims from database
  const fetchTokenRequiredClaims = async () => {
    setLoadingTokenClaims(true);
    try {
      const response = await fetch(`/api/tokens/${tokenAddress}`);
      if (response.ok) {
        const tokenData = await response.json();
        if (tokenData.selectedClaims) {
          // Parse comma-separated claims or array
          const claims = typeof tokenData.selectedClaims === 'string'
            ? tokenData.selectedClaims.split(',').map((c: string) => c.trim())
            : tokenData.selectedClaims;
          setTokenRequiredClaims(claims);
        } else {
          // Default claims if none specified
          setTokenRequiredClaims(['KYC_CLAIM', 'QUALIFICATION_CLAIM']);
        }
      }
    } catch (error) {
      console.error('Error fetching token claims:', error);
      // Default claims on error
      setTokenRequiredClaims(['KYC_CLAIM', 'QUALIFICATION_CLAIM']);
    } finally {
      setLoadingTokenClaims(false);
    }
  };

  // Load token required claims on component mount
  useEffect(() => {
    fetchTokenRequiredClaims();
  }, [tokenAddress]);

  const fetchClaims = async () => {
    if (!walletAddress) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/claims?walletAddress=${walletAddress}`);
      const data: ClaimResponse = await response.json();

      if (response.ok) {
        setClaims(data.claims);
      } else {
        setError(data.error || 'Failed to fetch claims');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const issueClaim = async () => {
    if (!walletAddress || !newClaim.claimType) return;

    setIssuingClaim(true);
    setError('');

    try {
      const response = await fetch('/api/claims', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress,
          claimType: newClaim.claimType,
          data: newClaim.data || 'APPROVED',
          uri: newClaim.uri,
          expiresAt: newClaim.expiresAt || null
        }),
      });

      const data = await response.json();

      if (response.ok) {
        alert(`✅ Claim issued successfully!\nTransaction: ${data.transactionHash}`);
        // Refresh claims
        await fetchClaims();
        // Reset form
        setNewClaim({
          claimType: 'KYC_CLAIM',
          data: '',
          uri: '',
          expiresAt: ''
        });
      } else {
        setError(data.error || 'Failed to issue claim');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setIssuingClaim(false);
    }
  };

  const getClaimTypeInfo = (claimType: string) => {
    return claimTypes.find(t => t.value === claimType) || { label: claimType, description: 'Unknown claim type' };
  };

  const getStatusBadge = (claim: Claim) => {
    if (claim.revoked) {
      return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Revoked</span>;
    }
    if (claim.valid) {
      return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Valid</span>;
    }
    return <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Expired</span>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Extract Tokeny value from claim data
  const extractTokenyValue = (claim: Claim) => {
    try {
      // Try to decode the claim data to extract Tokeny format value
      if (claim.data && claim.data !== '0x') {
        // Attempt to decode as ABI-encoded data
        const decoded = ethers.AbiCoder.defaultAbiCoder().decode(['string', 'string', 'uint256'], claim.data);
        const tokenyValue = decoded[0]; // First parameter should be the Tokeny format value

        if (tokenyValue && tokenyValue.match(/^\d{14,20}$/)) {
          return tokenyValue;
        }
      }

      // Check URI for Tokeny format
      if (claim.uri && claim.uri.includes(':')) {
        const parts = claim.uri.split(':');
        const potentialValue = parts[1];
        if (potentialValue && potentialValue.match(/^\d{14,20}$/)) {
          return potentialValue;
        }
      }

      return 'N/A';
    } catch (error) {
      return 'N/A';
    }
  };

  // Parse Tokeny value to show human-readable format
  const parseTokenyValue = (tokenyValue: string) => {
    if (tokenyValue === 'N/A' || !tokenyValue.match(/^\d{14,20}$/)) {
      return { display: 'N/A', tooltip: 'Not in Tokeny format' };
    }

    try {
      // Format: YYMMDDHHMMSS + CountryCode + ClaimType
      const year = '20' + tokenyValue.slice(0, 2);
      const month = tokenyValue.slice(2, 4);
      const day = tokenyValue.slice(4, 6);
      const hour = tokenyValue.slice(6, 8);
      const minute = tokenyValue.slice(8, 10);
      const second = tokenyValue.slice(10, 12);
      const countryCode = tokenyValue.slice(12, 15);
      const claimTypeCode = tokenyValue.slice(15);

      const timestamp = `${year}-${month}-${day} ${hour}:${minute}:${second}`;

      // Country code mapping (simplified)
      const countryNames: { [key: string]: string } = {
        '840': 'USA', '124': 'CAN', '826': 'GBR', '276': 'DEU', '250': 'FRA',
        '380': 'ITA', '724': 'ESP', '528': 'NLD', '756': 'CHE', '36': 'AUS',
        '392': 'JPN', '702': 'SGP'
      };

      const country = countryNames[countryCode] || `Code:${countryCode}`;

      return {
        display: tokenyValue,
        tooltip: `${timestamp}, ${country}, Type:${claimTypeCode}`
      };
    } catch (error) {
      return { display: tokenyValue, tooltip: 'Invalid Tokeny format' };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">ERC-3643 Claims Management</h3>
        <p className="text-gray-600">
          Manage Tokeny-style blockchain claims for this token. Claims are shared across all tokens and provide
          decentralized verification of user qualifications using the ERC-3643 standard.
        </p>
      </div>

      {/* Token-Specific Claims Requirements */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Token Claims Requirements
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <h5 className="font-semibold text-gray-900 mb-2">Required Claims for This Token</h5>
            {loadingTokenClaims ? (
              <div className="text-sm text-gray-500">Loading...</div>
            ) : (
              <ul className="text-sm text-gray-700 space-y-1">
                {tokenRequiredClaims.map((claimType) => {
                  const claimInfo = claimTypes.find(ct => ct.value === claimType);
                  return (
                    <li key={claimType} className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      {claimInfo ? `${claimInfo.label} (Topic ${claimInfo.id})` : claimType}
                    </li>
                  );
                })}
                {tokenRequiredClaims.length === 0 && (
                  <li className="text-gray-500 italic">No specific claims required</li>
                )}
              </ul>
            )}
          </div>
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <h5 className="font-semibold text-gray-900 mb-2">Available Claims</h5>
            <ul className="text-sm text-gray-700 space-y-1">
              {claimTypes.filter(ct => !tokenRequiredClaims.includes(ct.value)).map((claimType) => (
                <li key={claimType.value} className="flex items-center">
                  <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                  {claimType.label} (Topic {claimType.id})
                </li>
              ))}
            </ul>
          </div>
          <div className="bg-white rounded-lg p-4 border border-blue-100">
            <h5 className="font-semibold text-gray-900 mb-2">Tokeny Format</h5>
            <div className="text-sm text-gray-700">
              <p className="mb-1">Format: <code className="bg-gray-100 px-1 rounded text-xs">YYMMDDHHMMSS + Country + Type</code></p>
              <p className="text-xs text-gray-500">Example: <code>241218143045840001</code></p>
              <p className="text-xs text-gray-500">Dec 18, 2024 14:30:45, USA, KYC</p>
            </div>
          </div>
        </div>
        <div className="mt-4 p-3 bg-blue-100 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>💡 Shared Claims:</strong> Claims issued for this token are automatically available for all other tokens.
            Users only need to complete verification once to access multiple token offerings.
          </p>
        </div>
      </div>

      {/* Search Section */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">Search Claims by Wallet</h4>
        <div className="flex space-x-4">
          <div className="flex-1">
            <label htmlFor="walletAddress" className="block text-sm font-medium text-gray-700 mb-2">
              Wallet Address
            </label>
            <input
              type="text"
              id="walletAddress"
              value={walletAddress}
              onChange={(e) => setWalletAddress(e.target.value)}
              placeholder="0x..."
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={fetchClaims}
              disabled={loading || !walletAddress}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
            >
              {loading ? 'Searching...' : 'Search Claims'}
            </button>
          </div>
        </div>
      </div>

      {/* Issue New Claim Section */}
      {walletAddress && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Issue New Claim</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Claim Type</label>
              <select
                value={newClaim.claimType}
                onChange={(e) => setNewClaim({ ...newClaim, claimType: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                {claimTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {getClaimTypeInfo(newClaim.claimType).description}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Data</label>
              <input
                type="text"
                value={newClaim.data}
                onChange={(e) => setNewClaim({ ...newClaim, data: e.target.value })}
                placeholder="APPROVED"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">Optional claim data (default: APPROVED)</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">URI (optional)</label>
              <input
                type="text"
                value={newClaim.uri}
                onChange={(e) => setNewClaim({ ...newClaim, uri: e.target.value })}
                placeholder="https://..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">Optional URI for additional claim information</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Expires At (optional)</label>
              <input
                type="datetime-local"
                value={newClaim.expiresAt}
                onChange={(e) => setNewClaim({ ...newClaim, expiresAt: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">Leave empty for permanent claim</p>
            </div>
          </div>
          <button
            onClick={issueClaim}
            disabled={issuingClaim || !walletAddress}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
          >
            {issuingClaim ? 'Issuing Claim...' : 'Issue Claim'}
          </button>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* Claims Results */}
      {claims.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h4 className="text-lg font-semibold text-gray-900">
              Claims for {walletAddress.slice(0, 6)}...{walletAddress.slice(-4)}
            </h4>
            <p className="text-sm text-gray-600">
              {claims.filter(c => c.valid).length} valid claims out of {claims.length} total
            </p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Claim Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tokeny Value
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issued At
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expires At
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Issuer
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {claims.map((claim, index) => {
                  const tokenyValue = extractTokenyValue(claim);
                  const parsedTokeny = parseTokenyValue(tokenyValue);

                  return (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {getClaimTypeInfo(claim.claimType).label}
                        </div>
                        <div className="text-sm text-gray-500">Topic {claim.claimTypeId}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-mono text-gray-900" title={parsedTokeny.tooltip}>
                          {parsedTokeny.display}
                        </div>
                        {parsedTokeny.display !== 'N/A' && (
                          <div className="text-xs text-gray-500">{parsedTokeny.tooltip}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(claim)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(claim.issuedAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {claim.expiresAt ? formatDate(claim.expiresAt) : 'Never'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                        {claim.issuer.slice(0, 6)}...{claim.issuer.slice(-4)}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* No Claims Message */}
      {walletAddress && !loading && claims.length === 0 && !error && (
        <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
          No claims found for this wallet address.
        </div>
      )}

      {/* Info Section */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          ERC-3643 Shared Claims System
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-semibold text-gray-900 mb-2">🔗 Shared Across Tokens</h5>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Claims are stored in a central ClaimRegistry contract</li>
              <li>• Once verified, users can access ALL tokens requiring the same claims</li>
              <li>• No need to repeat KYC/verification for each token</li>
              <li>• Reduces friction and improves user experience</li>
            </ul>
          </div>

          <div>
            <h5 className="font-semibold text-gray-900 mb-2">📜 Tokeny Standard Format</h5>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Format: <code className="bg-gray-100 px-1 rounded">YYMMDDHHMMSS + Country + Type</code></li>
              <li>• Timestamp-based for chronological tracking</li>
              <li>• Country-aware for jurisdiction compliance</li>
              <li>• Type-specific for different verification levels</li>
            </ul>
          </div>
        </div>

        <div className="mt-4 p-4 bg-white rounded-lg border border-green-100">
          <h5 className="font-semibold text-gray-900 mb-2">💡 How It Works</h5>
          <div className="text-sm text-gray-700">
            <p className="mb-2">
              <strong>1. Token Creation:</strong> When you create a token, you select which claims are required (KYC, Qualification, etc.)
            </p>
            <p className="mb-2">
              <strong>2. User Verification:</strong> Users complete verification once and receive blockchain claims in Tokeny format
            </p>
            <p className="mb-2">
              <strong>3. Multi-Token Access:</strong> Users can now access ANY token that requires the same claims without re-verification
            </p>
            <p>
              <strong>4. Compliance:</strong> All tokens automatically inherit the same compliance standards and verification levels
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
