import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/prisma';

// GET /api/tokens/[address]/clients - Get all clients for a specific token
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  try {
    const { address } = await params;
    
    // First, find the token by address
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true, name: true, symbol: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Get all clients with their approval status for this token
    const clients = await prisma.client.findMany({
      include: {
        tokenApprovals: {
          where: { tokenId: token.id },
          select: {
            id: true,
            approvalStatus: true,
            kycApproved: true,
            whitelistApproved: true,
            approvedBy: true,
            approvedAt: true,
            rejectedReason: true,
            notes: true,
            createdAt: true,
            updatedAt: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Transform the data to include approval status
    const clientsWithApproval = clients.map(client => {
      const approval = client.tokenApprovals[0]; // Should be only one per token
      
      return {
        id: client.id,
        firstName: client.firstName,
        lastName: client.lastName,
        email: client.email,
        phoneNumber: client.phoneNumber,
        nationality: client.nationality,
        kycStatus: client.kycStatus,
        isWhitelisted: client.isWhitelisted,
        walletAddress: client.walletAddress,
        agreementAccepted: client.agreementAccepted,
        createdAt: client.createdAt,
        updatedAt: client.updatedAt,
        // Token-specific approval data
        tokenApproval: approval ? {
          id: approval.id,
          approvalStatus: approval.approvalStatus,
          kycApproved: approval.kycApproved,
          whitelistApproved: approval.whitelistApproved,
          approvedBy: approval.approvedBy,
          approvedAt: approval.approvedAt,
          rejectedReason: approval.rejectedReason,
          notes: approval.notes,
          createdAt: approval.createdAt,
          updatedAt: approval.updatedAt
        } : null
      };
    });

    return NextResponse.json({
      token: {
        id: token.id,
        name: token.name,
        symbol: token.symbol,
        address: address
      },
      clients: clientsWithApproval,
      total: clientsWithApproval.length,
      approved: clientsWithApproval.filter(c => c.tokenApproval?.approvalStatus === 'APPROVED').length,
      pending: clientsWithApproval.filter(c => c.tokenApproval?.approvalStatus === 'PENDING' || !c.tokenApproval).length,
      rejected: clientsWithApproval.filter(c => c.tokenApproval?.approvalStatus === 'REJECTED').length
    });

  } catch (error) {
    console.error('Error fetching token clients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch token clients' },
      { status: 500 }
    );
  }
}

// POST /api/tokens/[address]/clients - Add/update client approval for token
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ address: string }> }
) {
  try {
    const { address } = await params;
    const body = await request.json();
    
    const { clientId, approvalStatus, kycApproved, whitelistApproved, notes, approvedBy } = body;

    if (!clientId) {
      return NextResponse.json(
        { error: 'Client ID is required' },
        { status: 400 }
      );
    }

    // Find the token
    const token = await prisma.token.findUnique({
      where: { address },
      select: { id: true }
    });

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      );
    }

    // Check if client exists
    const client = await prisma.client.findUnique({
      where: { id: clientId },
      select: { id: true }
    });

    if (!client) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Create or update the approval record
    const approvalData = {
      approvalStatus: approvalStatus || 'PENDING',
      kycApproved: kycApproved || false,
      whitelistApproved: whitelistApproved || false,
      notes: notes || null,
      approvedBy: approvedBy || null,
      approvedAt: (approvalStatus === 'APPROVED') ? new Date() : null
    };

    const approval = await prisma.tokenClientApproval.upsert({
      where: {
        tokenId_clientId: {
          tokenId: token.id,
          clientId: clientId
        }
      },
      update: approvalData,
      create: {
        tokenId: token.id,
        clientId: clientId,
        ...approvalData
      },
      include: {
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            walletAddress: true
          }
        }
      }
    });

    return NextResponse.json(approval, { status: 201 });

  } catch (error) {
    console.error('Error creating/updating client approval:', error);
    
    // Handle Prisma constraint errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Client approval already exists for this token' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create/update client approval' },
      { status: 500 }
    );
  }
}
