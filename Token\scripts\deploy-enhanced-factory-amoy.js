const { ethers, upgrades } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
    console.log("🚀 Deploying Enhanced Factory with Secure Force Transfer to Amoy...");
    
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

    try {
        // Step 1: Deploy ClaimRegistry
        console.log("\n📋 Deploying ClaimRegistry...");
        const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
        const claimRegistry = await upgrades.deployProxy(
            ClaimRegistry,
            [deployer.address],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0
            }
        );
        await claimRegistry.waitForDeployment();
        const claimRegistryAddress = await claimRegistry.getAddress();
        console.log("✅ ClaimRegistry deployed to:", claimRegistryAddress);

        // Step 2: Deploy IdentityRegistry
        console.log("\n🆔 Deploying IdentityRegistry...");
        const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
        const identityRegistry = await upgrades.deployProxy(
            IdentityRegistry,
            [deployer.address, claimRegistryAddress],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0
            }
        );
        await identityRegistry.waitForDeployment();
        const identityRegistryAddress = await identityRegistry.getAddress();
        console.log("✅ IdentityRegistry deployed to:", identityRegistryAddress);

        // Step 3: Deploy Compliance
        console.log("\n📊 Deploying Compliance...");
        const Compliance = await ethers.getContractFactory("Compliance");
        const compliance = await upgrades.deployProxy(
            Compliance,
            [deployer.address, identityRegistryAddress],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0
            }
        );
        await compliance.waitForDeployment();
        const complianceAddress = await compliance.getAddress();
        console.log("✅ Compliance deployed to:", complianceAddress);

        // Step 4: Deploy Enhanced SecurityToken Implementation
        console.log("\n🔒 Deploying Enhanced SecurityToken Implementation...");
        const SecurityToken = await ethers.getContractFactory("SecurityToken");
        const securityTokenImpl = await SecurityToken.deploy();
        await securityTokenImpl.waitForDeployment();
        const securityTokenImplAddress = await securityTokenImpl.getAddress();
        console.log("✅ Enhanced SecurityToken Implementation deployed to:", securityTokenImplAddress);

        // Step 5: Deploy UpgradeManager
        console.log("\n⬆️ Deploying UpgradeManager...");
        const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
        const upgradeManager = await upgrades.deployProxy(
            UpgradeManager,
            [deployer.address],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0
            }
        );
        await upgradeManager.waitForDeployment();
        const upgradeManagerAddress = await upgradeManager.getAddress();
        console.log("✅ UpgradeManager deployed to:", upgradeManagerAddress);

        // Step 6: Deploy Enhanced Factory
        console.log("\n🏭 Deploying Enhanced SecurityTokenFactory...");
        const SecurityTokenFactory = await ethers.getContractFactory("SecurityTokenFactory");
        const factory = await SecurityTokenFactory.deploy(deployer.address);
        await factory.waitForDeployment();
        const factoryAddress = await factory.getAddress();
        console.log("✅ Enhanced SecurityTokenFactory deployed to:", factoryAddress);

        // Step 7: Grant deployer role to factory
        console.log("\n🔑 Setting up factory permissions...");
        const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
        await factory.grantRole(DEPLOYER_ROLE, deployer.address);
        console.log("✅ Deployer role granted to:", deployer.address);

        // Step 8: Deploy a test token using the new factory
        console.log("\n🪙 Deploying test token with enhanced force transfer...");
        const tokenParams = {
            name: "Enhanced Security Token",
            symbol: "EST",
            decimals: 0,
            maxSupply: ethers.parseUnits("1000000", 0),
            tokenPrice: "1.50 USD",
            bonusTiers: "Early: 15%, Standard: 10%",
            tokenDetails: "Security token with graduated force transfer compliance and multi-signature authorization",
            tokenImageUrl: ""
        };

        const deployTx = await factory.deploySecurityToken(
            tokenParams.name,
            tokenParams.symbol,
            tokenParams.decimals,
            tokenParams.maxSupply,
            deployer.address, // admin
            tokenParams.tokenPrice,
            tokenParams.bonusTiers,
            tokenParams.tokenDetails,
            tokenParams.tokenImageUrl
        );

        const receipt = await deployTx.wait();

        // Find the SecurityTokenDeployed event
        const tokenDeployedEvent = receipt.logs.find(log => {
            try {
                const parsed = factory.interface.parseLog(log);
                return parsed.name === "SecurityTokenDeployed";
            } catch {
                return false;
            }
        });

        if (!tokenDeployedEvent) {
            throw new Error("SecurityTokenDeployed event not found");
        }

        const parsedEvent = factory.interface.parseLog(tokenDeployedEvent);
        const tokenAddress = parsedEvent.args.tokenAddress;
        console.log("✅ Enhanced Security Token deployed to:", tokenAddress);

        // Step 9: Test the enhanced force transfer functionality
        console.log("\n🧪 Testing enhanced force transfer functionality...");
        const token = await ethers.getContractAt("SecurityToken", tokenAddress);

        // Check force transfer configuration
        const ForceTransferType = {
            EMERGENCY_SECURITY: 0,
            REGULATORY_ORDER: 1,
            CORPORATE_ACTION: 2,
            RECOVERY_ASSISTANCE: 3
        };

        console.log("📋 Force Transfer Configuration:");
        for (const [typeName, typeValue] of Object.entries(ForceTransferType)) {
            const requiredSigs = await token.getRequiredSignatures(typeValue);
            console.log(`   ${typeName}: ${requiredSigs} signature(s) required`);
        }

        // Test that we have the new role
        const FORCE_TRANSFER_AUTHORIZER_ROLE = await token.FORCE_TRANSFER_AUTHORIZER_ROLE();
        const hasRole = await token.hasRole(FORCE_TRANSFER_AUTHORIZER_ROLE, deployer.address);
        console.log("✅ Deployer has FORCE_TRANSFER_AUTHORIZER_ROLE:", hasRole);

        // Set up test scenario
        await identityRegistry.registerIdentity(deployer.address, 840); // USA
        await identityRegistry.addToWhitelist(deployer.address);
        await identityRegistry.approveKyc(deployer.address);

        const testRecipient = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6";
        await identityRegistry.registerIdentity(testRecipient, 840);
        await identityRegistry.addToWhitelist(testRecipient);
        await identityRegistry.approveKyc(testRecipient);

        // Mint tokens
        await token.mint(deployer.address, 1000);
        console.log("✅ Minted 1000 tokens to deployer");

        // Test legacy force transfer still works
        await token.forcedTransfer(deployer.address, testRecipient, 100);
        console.log("✅ Legacy force transfer works");

        // Test new secure force transfer initiation
        const transferHash = await token.initiateSecureForceTransfer.staticCall(
            deployer.address,
            testRecipient,
            50,
            ForceTransferType.EMERGENCY_SECURITY,
            "Test emergency transfer"
        );
        console.log("✅ Secure force transfer can be initiated");

        // Step 10: Save deployment information
        const deploymentInfo = {
            network: "amoy",
            chainId: "80002",
            timestamp: new Date().toISOString(),
            deployer: deployer.address,
            contracts: {
                claimRegistry: claimRegistryAddress,
                identityRegistry: identityRegistryAddress,
                compliance: complianceAddress,
                securityTokenImplementation: securityTokenImplAddress,
                upgradeManager: upgradeManagerAddress,
                factory: factoryAddress,
                testToken: tokenAddress
            },
            tokenInfo: {
                ...tokenParams,
                maxSupply: tokenParams.maxSupply.toString(),
                address: tokenAddress
            },
            forceTransferConfig: {
                EMERGENCY_SECURITY: await token.getRequiredSignatures(0),
                REGULATORY_ORDER: await token.getRequiredSignatures(1),
                CORPORATE_ACTION: await token.getRequiredSignatures(2),
                RECOVERY_ASSISTANCE: await token.getRequiredSignatures(3)
            },
            features: {
                secureForceTransfer: true,
                graduatedCompliance: true,
                multiSignatureAuth: true,
                timeDelayedExecution: true,
                backwardCompatible: true,
                enhancedAuditTrail: true,
                factoryDeployment: true,
                amoyTestnet: true
            }
        };

        const deploymentPath = path.join(__dirname, '../deployments/amoy-enhanced-force-transfer.json');
        fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
        console.log(`\n💾 Deployment info saved to: ${deploymentPath}`);

        // Step 11: Display summary
        console.log("\n🎉 ENHANCED FACTORY DEPLOYMENT COMPLETE!");
        console.log("=" .repeat(70));
        console.log("🌐 Network: Amoy Testnet");
        console.log("📋 ClaimRegistry:", claimRegistryAddress);
        console.log("🆔 IdentityRegistry:", identityRegistryAddress);
        console.log("📊 Compliance:", complianceAddress);
        console.log("🔒 SecurityToken Implementation:", securityTokenImplAddress);
        console.log("⬆️ UpgradeManager:", upgradeManagerAddress);
        console.log("🏭 Enhanced Factory:", factoryAddress);
        console.log("🪙 Test Token:", tokenAddress);
        console.log("=" .repeat(70));
        console.log("\n✨ Enhanced Features Available:");
        console.log("• Graduated force transfer compliance");
        console.log("• Multi-signature authorization system");
        console.log("• Time-delayed execution for non-emergency transfers");
        console.log("• Enhanced audit trail with detailed events");
        console.log("• Backward compatibility with legacy force transfers");
        console.log("• Factory-based deployment for large contracts");
        console.log("\n🔧 Next Steps:");
        console.log("1. Update admin panel to use new factory address");
        console.log("2. Add additional force transfer authorizers");
        console.log("3. Configure signature requirements for your organization");
        console.log("4. Deploy production tokens using the enhanced factory");

        return deploymentInfo;

    } catch (error) {
        console.error("❌ Deployment failed:", error);
        throw error;
    }
}

// Execute deployment
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;
