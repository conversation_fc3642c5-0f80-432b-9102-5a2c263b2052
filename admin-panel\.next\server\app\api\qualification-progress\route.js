/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/qualification-progress/route";
exports.ids = ["app/api/qualification-progress/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/qualification-progress/route.ts */ \"(rsc)/./src/app/api/qualification-progress/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/qualification-progress/route\",\n        pathname: \"/api/qualification-progress\",\n        filename: \"route\",\n        bundlePath: \"app/api/qualification-progress/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\qualification-progress\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_qualification_progress_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/qualification-progress/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/qualification-progress/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const clientEmail = searchParams.get('clientEmail');\n        const tokenAddress = searchParams.get('tokenAddress');\n        const clientId = searchParams.get('clientId');\n        if (!clientEmail && !clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client email or ID required'\n            }, {\n                status: 400\n            });\n        }\n        // Find client first\n        let client;\n        if (clientId) {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n                where: {\n                    id: clientId\n                }\n            });\n        } else {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n                where: {\n                    email: clientEmail\n                }\n            });\n        }\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find qualification progress\n        const whereClause = {\n            clientId: client.id\n        };\n        if (tokenAddress) {\n            // Find token first\n            const token = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findFirst({\n                where: {\n                    address: tokenAddress\n                }\n            });\n            if (token) {\n                whereClause.tokenId = token.id;\n            } else {\n                // If token not found, return default progress\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    country: '',\n                    countryCompleted: false,\n                    agreementAccepted: false,\n                    profileCompleted: !!client,\n                    walletConnected: !!client.walletAddress,\n                    kycCompleted: client.kycStatus === 'APPROVED',\n                    currentStep: 0,\n                    completedSteps: 0,\n                    tokenAddress: tokenAddress,\n                    clientEmail: client.email,\n                    lastUpdated: new Date().toISOString()\n                });\n            }\n        }\n        const progress = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.findFirst({\n            where: whereClause,\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        if (!progress) {\n            // Return default progress based on client status\n            const defaultProgress = {\n                country: '',\n                countryCompleted: false,\n                agreementAccepted: false,\n                profileCompleted: !!client,\n                walletConnected: !!client.walletAddress,\n                kycCompleted: client.kycStatus === 'APPROVED',\n                currentStep: 0,\n                completedSteps: 0,\n                tokenAddress: tokenAddress,\n                clientEmail: client.email,\n                lastUpdated: new Date().toISOString()\n            };\n            // Calculate current step based on completion status\n            if (defaultProgress.kycCompleted) {\n                defaultProgress.currentStep = 5;\n                defaultProgress.completedSteps = 5;\n            } else if (defaultProgress.walletConnected) {\n                defaultProgress.currentStep = 4;\n                defaultProgress.completedSteps = 4;\n            } else if (defaultProgress.profileCompleted) {\n                defaultProgress.currentStep = 3;\n                defaultProgress.completedSteps = 3;\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(defaultProgress);\n        }\n        // Return existing progress\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            country: progress.countryValue || '',\n            countryCompleted: progress.countrySelected,\n            agreementAccepted: progress.agreementAccepted,\n            profileCompleted: progress.profileCompleted,\n            walletConnected: progress.walletConnected,\n            kycCompleted: progress.kycCompleted,\n            currentStep: progress.currentStep,\n            completedSteps: progress.completedSteps,\n            qualificationStatus: progress.qualificationStatus,\n            approvedBy: progress.approvedBy,\n            approvedAt: progress.approvedAt?.toISOString(),\n            rejectedReason: progress.rejectedReason,\n            tokenAddress: progress.token?.address || tokenAddress,\n            clientEmail: progress.client.email,\n            lastUpdated: progress.updatedAt.toISOString()\n        });\n    } catch (error) {\n        console.error('Error fetching qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { clientEmail, clientId, tokenAddress, country, countryCompleted, agreementAccepted, profileCompleted, walletConnected, kycCompleted, currentStep, completedSteps } = body;\n        if (!clientEmail && !clientId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client email or ID required'\n            }, {\n                status: 400\n            });\n        }\n        // Find client\n        let client;\n        if (clientId) {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n                where: {\n                    id: clientId\n                }\n            });\n        } else {\n            client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findFirst({\n                where: {\n                    email: clientEmail\n                }\n            });\n        }\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find token if provided\n        let token = null;\n        if (tokenAddress) {\n            token = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findFirst({\n                where: {\n                    address: tokenAddress\n                }\n            });\n        }\n        // Create or update qualification progress\n        const progressData = {\n            clientId: client.id,\n            tokenId: token?.id,\n            countrySelected: countryCompleted || false,\n            countryValue: country || '',\n            agreementAccepted: agreementAccepted || false,\n            profileCompleted: profileCompleted || false,\n            walletConnected: walletConnected || false,\n            kycCompleted: kycCompleted || false,\n            currentStep: currentStep || 0,\n            completedSteps: completedSteps || 0,\n            // Set completion timestamps\n            countryCompletedAt: countryCompleted ? new Date() : null,\n            agreementCompletedAt: agreementAccepted ? new Date() : null,\n            profileCompletedAt: profileCompleted ? new Date() : null,\n            walletCompletedAt: walletConnected ? new Date() : null,\n            kycCompletedAt: kycCompleted ? new Date() : null\n        };\n        const whereClause = {\n            clientId: client.id\n        };\n        if (token) {\n            whereClause.tokenId = token.id;\n        } else if (!tokenAddress) {\n            // For global progress (no specific token)\n            whereClause.tokenId = null;\n        }\n        const progress = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.upsert({\n            where: {\n                clientId_tokenId: whereClause\n            },\n            update: progressData,\n            create: progressData,\n            include: {\n                client: true,\n                token: true\n            }\n        });\n        console.log('💾 Saved qualification progress to database:', {\n            clientEmail: client.email,\n            tokenAddress: token?.address || 'global',\n            currentStep: progressData.currentStep,\n            completedSteps: progressData.completedSteps,\n            flags: {\n                countrySelected: progressData.countrySelected,\n                agreementAccepted: progressData.agreementAccepted,\n                profileCompleted: progressData.profileCompleted,\n                walletConnected: progressData.walletConnected,\n                kycCompleted: progressData.kycCompleted\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Qualification progress saved successfully',\n            data: {\n                id: progress.id,\n                currentStep: progress.currentStep,\n                completedSteps: progress.completedSteps,\n                lastUpdated: progress.updatedAt.toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error saving qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to save qualification progress'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT endpoint to fix qualification progress flags\nasync function PUT(request) {\n    try {\n        const { userEmail, tokenAddress } = await request.json();\n        if (!userEmail || !tokenAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Find the client\n        const client = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.client.findUnique({\n            where: {\n                email: userEmail\n            }\n        });\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Client not found'\n            }, {\n                status: 404\n            });\n        }\n        // Find existing qualification progress\n        const existingProgress = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.findFirst({\n            where: {\n                clientId: client.id,\n                tokenAddress: tokenAddress\n            }\n        });\n        if (!existingProgress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Qualification progress not found'\n            }, {\n                status: 404\n            });\n        }\n        // Calculate correct completion flags based on current state\n        const countrySelected = existingProgress.completedSteps >= 1 || !!existingProgress.countryValue;\n        const agreementAccepted = existingProgress.completedSteps >= 2;\n        const profileCompleted = existingProgress.completedSteps >= 3 || !!client.firstName;\n        const walletConnected = existingProgress.completedSteps >= 4 || !!client.walletAddress;\n        const kycCompleted = existingProgress.completedSteps >= 5 || client.kycStatus === 'APPROVED';\n        // Update the qualification progress with correct flags\n        const updatedProgress = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.qualificationProgress.update({\n            where: {\n                id: existingProgress.id\n            },\n            data: {\n                countrySelected,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted,\n                // Set country value if not set\n                countryValue: existingProgress.countryValue || (countrySelected ? 'Fixed' : '')\n            }\n        });\n        console.log('🔧 Fixed qualification progress flags:', {\n            clientEmail: client.email,\n            tokenAddress,\n            before: {\n                countrySelected: existingProgress.countrySelected,\n                agreementAccepted: existingProgress.agreementAccepted,\n                profileCompleted: existingProgress.profileCompleted,\n                walletConnected: existingProgress.walletConnected,\n                kycCompleted: existingProgress.kycCompleted\n            },\n            after: {\n                countrySelected,\n                agreementAccepted,\n                profileCompleted,\n                walletConnected,\n                kycCompleted\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Qualification progress flags fixed successfully',\n            progress: updatedProgress\n        });\n    } catch (error) {\n        console.error('Error fixing qualification progress:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/qualification-progress/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqualification-progress%2Froute&page=%2Fapi%2Fqualification-progress%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqualification-progress%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();