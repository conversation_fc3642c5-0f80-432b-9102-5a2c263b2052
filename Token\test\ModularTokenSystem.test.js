const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("Modular Token System - Comprehensive Tests", function () {
  let securityTokenCore;
  let identityRegistry;
  let compliance;
  let kycClaimsModule;
  let claimRegistry;
  let upgradeManager;
  let owner, agent, investor1, investor2, unauthorized;

  beforeEach(async function () {
    [owner, agent, investor1, investor2, unauthorized] = await ethers.getSigners();

    // Deploy ClaimRegistry first (not upgradeable)
    const ClaimRegistry = await ethers.getContractFactory("SimpleClaimRegistry");
    claimRegistry = await ClaimRegistry.deploy(owner.address);
    await claimRegistry.waitForDeployment();

    // Deploy IdentityRegistry
    const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
    identityRegistry = await upgrades.deployProxy(IdentityRegistry, [owner.address], { initializer: "initialize" });
    await identityRegistry.waitForDeployment();

    // Deploy Compliance
    const Compliance = await ethers.getContractFactory("Compliance");
    compliance = await upgrades.deployProxy(
      Compliance,
      [await identityRegistry.getAddress(), owner.address],
      { initializer: "initialize" }
    );
    await compliance.waitForDeployment();

    // Deploy KYCClaimsModule
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    kycClaimsModule = await upgrades.deployProxy(
      KYCClaimsModule,
      [await claimRegistry.getAddress(), owner.address],
      { initializer: "initialize" }
    );
    await kycClaimsModule.waitForDeployment();

    // Deploy UpgradeManager
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    upgradeManager = await upgrades.deployProxy(UpgradeManager, [owner.address], { initializer: "initialize" });
    await upgradeManager.waitForDeployment();

    // Deploy SecurityTokenCore
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    securityTokenCore = await upgrades.deployProxy(
      SecurityTokenCore,
      [
        "Modular Test Token",
        "MTT",
        0, // 0 decimals
        ethers.parseUnits("1000000", 0), // 1M tokens with 0 decimals
        await identityRegistry.getAddress(),
        owner.address,
        "5.00 USD",
        "Early: 20%, Standard: 15%",
        "Test token for modular system",
        ""
      ],
      { initializer: "initialize" }
    );
    await securityTokenCore.waitForDeployment();

    // Set up roles
    await securityTokenCore.grantRole(await securityTokenCore.AGENT_ROLE(), agent.address);
    await identityRegistry.grantRole(await identityRegistry.AGENT_ROLE(), agent.address);
    await compliance.grantRole(await compliance.AGENT_ROLE(), agent.address);
    await kycClaimsModule.grantRole(await kycClaimsModule.CLAIM_ISSUER_ROLE(), agent.address);

    // Set token address in compliance
    await compliance.setTokenAddress(await securityTokenCore.getAddress());

    // Configure KYC module for the token
    await kycClaimsModule.configureTokenClaims(
      await securityTokenCore.getAddress(),
      [], // No custom claims required
      true, // KYC enabled
      true  // Claims enabled
    );
  });

  describe("SecurityTokenCore - Basic Functionality", function () {
    it("Should initialize with correct parameters", async function () {
      expect(await securityTokenCore.name()).to.equal("Modular Test Token");
      expect(await securityTokenCore.symbol()).to.equal("MTT");
      expect(await securityTokenCore.decimals()).to.equal(0);
      expect(await securityTokenCore.maxSupply()).to.equal(ethers.parseUnits("1000000", 0));
      expect(await securityTokenCore.totalSupply()).to.equal(0);
    });

    it("Should have correct roles set up", async function () {
      expect(await securityTokenCore.hasRole(await securityTokenCore.DEFAULT_ADMIN_ROLE(), owner.address)).to.be.true;
      expect(await securityTokenCore.hasRole(await securityTokenCore.AGENT_ROLE(), agent.address)).to.be.true;
      expect(await securityTokenCore.hasRole(await securityTokenCore.TRANSFER_MANAGER_ROLE(), owner.address)).to.be.true;
    });

    it("Should be pausable", async function () {
      await securityTokenCore.pause();
      expect(await securityTokenCore.paused()).to.be.true;

      await securityTokenCore.unpause();
      expect(await securityTokenCore.paused()).to.be.false;
    });
  });

  describe("Identity Registry Integration", function () {
    it("Should register and verify identities", async function () {
      // Register investor1
      await identityRegistry.connect(agent).registerIdentity(
        investor1.address,
        ethers.encodeBytes32String("INVESTOR1_ID"),
        840 // USA country code
      );

      expect(await identityRegistry.isVerified(investor1.address)).to.be.true;
      expect(await identityRegistry.investorCountry(investor1.address)).to.equal(840);
    });

    it("Should manage whitelist", async function () {
      // Register and whitelist investor1
      await identityRegistry.connect(agent).registerIdentity(
        investor1.address,
        ethers.encodeBytes32String("INVESTOR1_ID"),
        840
      );
      await identityRegistry.connect(agent).addToWhitelist(investor1.address);

      expect(await identityRegistry.isWhitelisted(investor1.address)).to.be.true;
    });
  });

  describe("KYC Claims Module", function () {
    beforeEach(async function () {
      // Register investors
      await identityRegistry.connect(agent).registerIdentity(
        investor1.address,
        ethers.encodeBytes32String("INVESTOR1_ID"),
        840
      );
      await identityRegistry.connect(agent).registerIdentity(
        investor2.address,
        ethers.encodeBytes32String("INVESTOR2_ID"),
        840
      );
    });

    it("Should approve KYC traditionally", async function () {
      await kycClaimsModule.connect(agent).approveKYC(
        await securityTokenCore.getAddress(),
        investor1.address
      );

      expect(await kycClaimsModule.isKYCApproved(
        await securityTokenCore.getAddress(),
        investor1.address
      )).to.be.true;
    });

    it("Should add to whitelist traditionally", async function () {
      await kycClaimsModule.connect(agent).addToWhitelist(
        await securityTokenCore.getAddress(),
        investor1.address
      );

      expect(await kycClaimsModule.isWhitelisted(
        await securityTokenCore.getAddress(),
        investor1.address
      )).to.be.true;
    });

    it("Should check eligibility", async function () {
      // Approve KYC and whitelist
      await kycClaimsModule.connect(agent).approveKYC(
        await securityTokenCore.getAddress(),
        investor1.address
      );

      expect(await kycClaimsModule.isEligible(
        await securityTokenCore.getAddress(),
        investor1.address
      )).to.be.true;
    });

    it("Should get verification status", async function () {
      await kycClaimsModule.connect(agent).approveKYC(
        await securityTokenCore.getAddress(),
        investor1.address
      );

      const [kycApproved, whitelisted, eligible, method] = await kycClaimsModule.getVerificationStatus(
        await securityTokenCore.getAddress(),
        investor1.address
      );

      expect(kycApproved).to.be.true;
      expect(whitelisted).to.be.true;
      expect(eligible).to.be.true;
      expect(method).to.equal("TRADITIONAL");
    });
  });

  describe("Compliance Engine", function () {
    beforeEach(async function () {
      // Set up investors
      await identityRegistry.connect(agent).registerIdentity(
        investor1.address,
        ethers.encodeBytes32String("INVESTOR1_ID"),
        840
      );
      await identityRegistry.connect(agent).addToWhitelist(investor1.address);

      await identityRegistry.connect(agent).registerIdentity(
        investor2.address,
        ethers.encodeBytes32String("INVESTOR2_ID"),
        840
      );
      await identityRegistry.connect(agent).addToWhitelist(investor2.address);
    });

    it("Should check transfer compliance", async function () {
      // Should be compliant for whitelisted addresses
      expect(await compliance.canTransfer(
        investor1.address,
        investor2.address,
        ethers.parseUnits("100", 0)
      )).to.be.true;
    });

    it("Should track transfers", async function () {
      // Simulate a transfer
      await compliance.transferred(
        investor1.address,
        investor2.address,
        ethers.parseUnits("100", 0)
      );

      const [totalTransfers, lastTransferTime] = await compliance.getTransferStats();
      expect(totalTransfers).to.equal(1);
      expect(lastTransferTime).to.be.greaterThan(0);
    });

    it("Should manage compliance rules", async function () {
      const ruleId = ethers.keccak256(ethers.toUtf8Bytes("TEST_RULE"));
      
      await compliance.connect(agent).addComplianceRule(
        ruleId,
        "Test Rule",
        1000, // max holders
        ethers.parseUnits("10000", 0), // max tokens per holder
        ethers.parseUnits("1000000", 0) // max total supply
      );

      const [isActive, maxHolders, maxTokensPerHolder, maxTotalSupply] = await compliance.getComplianceRule(ruleId);
      expect(isActive).to.be.true;
      expect(maxHolders).to.equal(1000);
      expect(maxTokensPerHolder).to.equal(ethers.parseUnits("10000", 0));
      expect(maxTotalSupply).to.equal(ethers.parseUnits("1000000", 0));
    });
  });

  describe("Token Operations", function () {
    beforeEach(async function () {
      // Set up investor1 with KYC and whitelist
      await identityRegistry.connect(agent).registerIdentity(
        investor1.address,
        ethers.encodeBytes32String("INVESTOR1_ID"),
        840
      );
      await identityRegistry.connect(agent).addToWhitelist(investor1.address);
      await kycClaimsModule.connect(agent).approveKYC(
        await securityTokenCore.getAddress(),
        investor1.address
      );

      // Set up investor2
      await identityRegistry.connect(agent).registerIdentity(
        investor2.address,
        ethers.encodeBytes32String("INVESTOR2_ID"),
        840
      );
      await identityRegistry.connect(agent).addToWhitelist(investor2.address);
      await kycClaimsModule.connect(agent).approveKYC(
        await securityTokenCore.getAddress(),
        investor2.address
      );
    });

    it("Should mint tokens to verified investors", async function () {
      await securityTokenCore.connect(agent).mint(investor1.address, ethers.parseUnits("1000", 0));
      
      expect(await securityTokenCore.balanceOf(investor1.address)).to.equal(ethers.parseUnits("1000", 0));
      expect(await securityTokenCore.totalSupply()).to.equal(ethers.parseUnits("1000", 0));
    });

    it("Should burn tokens", async function () {
      await securityTokenCore.connect(agent).mint(investor1.address, ethers.parseUnits("1000", 0));
      await securityTokenCore.connect(agent).burn(investor1.address, ethers.parseUnits("500", 0));
      
      expect(await securityTokenCore.balanceOf(investor1.address)).to.equal(ethers.parseUnits("500", 0));
      expect(await securityTokenCore.totalSupply()).to.equal(ethers.parseUnits("500", 0));
    });

    it("Should perform force transfer", async function () {
      // Mint tokens to investor1
      await securityTokenCore.connect(agent).mint(investor1.address, ethers.parseUnits("1000", 0));
      
      // Force transfer from investor1 to investor2
      await securityTokenCore.forcedTransfer(
        investor1.address,
        investor2.address,
        ethers.parseUnits("500", 0)
      );
      
      expect(await securityTokenCore.balanceOf(investor1.address)).to.equal(ethers.parseUnits("500", 0));
      expect(await securityTokenCore.balanceOf(investor2.address)).to.equal(ethers.parseUnits("500", 0));
    });
  });

  describe("Upgrade Manager", function () {
    it("Should register modules", async function () {
      const moduleId = ethers.keccak256(ethers.toUtf8Bytes("TEST_MODULE"));
      
      await upgradeManager.registerModule(moduleId, investor1.address);
      
      expect(await upgradeManager.isModuleRegistered(moduleId)).to.be.true;
      expect(await upgradeManager.getModuleAddress(moduleId)).to.equal(investor1.address);
    });

    it("Should manage emergency mode", async function () {
      await upgradeManager.activateEmergencyMode();
      expect(await upgradeManager.emergencyMode()).to.be.true;
      
      await upgradeManager.deactivateEmergencyMode();
      expect(await upgradeManager.emergencyMode()).to.be.false;
    });
  });
});
