const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("Advanced Transfer Controls", function () {
  let securityToken;
  let identityRegistry;
  let owner, agent, investor1, investor2, feeCollector, unauthorized;

  beforeEach(async function () {
    [owner, agent, investor1, investor2, feeCollector, unauthorized] = await ethers.getSigners();

    // Deploy Identity Registry using upgrades
    const IdentityRegistry = await ethers.getContractFactory("Whitelist");
    identityRegistry = await upgrades.deployProxy(IdentityRegistry, [owner.address], { initializer: "initialize" });
    await identityRegistry.waitForDeployment();

    // Deploy Security Token using upgrades
    const SecurityToken = await ethers.getContractFactory("SecurityToken");
    securityToken = await upgrades.deployProxy(
      SecurityToken,
      [
        "Test Token",
        "TEST",
        18,
        ethers.parseEther("1000000"),
        await identityRegistry.getAddress(),
        owner.address,
        "10 USD",
        "Tier 1: 5%",
        "Test token for advanced transfer controls",
        ""
      ],
      { initializer: "initialize" }
    );
    await securityToken.waitForDeployment();

    // Add agent to both contracts
    await securityToken.addAgent(agent.address);
    await identityRegistry.addAgent(agent.address);

    // Whitelist investors and fee collector
    await identityRegistry.connect(agent).addToWhitelist(investor1.address);
    await identityRegistry.connect(agent).addToWhitelist(investor2.address);
    await identityRegistry.connect(agent).addToWhitelist(feeCollector.address);

    // Mint some tokens to investor1
    await securityToken.connect(agent).mint(investor1.address, ethers.parseEther("1000"));
  });

  describe("Conditional Transfers", function () {
    beforeEach(async function () {
      // Enable conditional transfers
      await securityToken.setConditionalTransfers(true);
    });

    it("Should enable/disable conditional transfers", async function () {
      expect(await securityToken.conditionalTransfersEnabled()).to.be.true;

      await securityToken.setConditionalTransfers(false);
      expect(await securityToken.conditionalTransfersEnabled()).to.be.false;
    });

    it("Should prevent regular transfers when conditional transfers are enabled", async function () {
      await expect(
        securityToken.connect(investor1).transfer(investor2.address, ethers.parseEther("100"))
      ).to.be.revertedWith("SecurityToken: transfer requires approval");
    });

    it("Should allow approved transfers", async function () {
      const amount = ethers.parseEther("100");
      const nonce = await securityToken.getTransferNonce(investor1.address);

      // Agent approves the transfer
      await securityToken.connect(agent).approveTransfer(
        investor1.address,
        investor2.address,
        amount,
        nonce
      );

      // Execute the approved transfer
      await securityToken.connect(investor1).executeApprovedTransfer(
        investor2.address,
        amount,
        nonce
      );

      expect(await securityToken.balanceOf(investor2.address)).to.equal(amount);
      expect(await securityToken.getTransferNonce(investor1.address)).to.equal(nonce + 1n);
    });

    it("Should prevent executing transfer with wrong nonce", async function () {
      const amount = ethers.parseEther("100");
      const wrongNonce = 999;

      await expect(
        securityToken.connect(investor1).executeApprovedTransfer(
          investor2.address,
          amount,
          wrongNonce
        )
      ).to.be.revertedWith("SecurityToken: invalid nonce");
    });

    it("Should prevent executing unapproved transfer", async function () {
      const amount = ethers.parseEther("100");
      const nonce = await securityToken.getTransferNonce(investor1.address);

      await expect(
        securityToken.connect(investor1).executeApprovedTransfer(
          investor2.address,
          amount,
          nonce
        )
      ).to.be.revertedWith("SecurityToken: transfer not approved");
    });

    it("Should emit TransferApproved event", async function () {
      const amount = ethers.parseEther("100");
      const nonce = await securityToken.getTransferNonce(investor1.address);

      await expect(
        securityToken.connect(agent).approveTransfer(
          investor1.address,
          investor2.address,
          amount,
          nonce
        )
      ).to.emit(securityToken, "TransferApproved");
    });
  });

  describe("Transfer Whitelisting", function () {
    beforeEach(async function () {
      // Enable transfer whitelisting
      await securityToken.setTransferWhitelist(true);
    });

    it("Should enable/disable transfer whitelisting", async function () {
      expect(await securityToken.transferWhitelistEnabled()).to.be.true;

      await securityToken.setTransferWhitelist(false);
      expect(await securityToken.transferWhitelistEnabled()).to.be.false;
    });

    it("Should prevent transfers from non-whitelisted addresses", async function () {
      await expect(
        securityToken.connect(investor1).transfer(investor2.address, ethers.parseEther("100"))
      ).to.be.revertedWith("SecurityToken: sender not transfer whitelisted");
    });

    it("Should allow transfers from whitelisted addresses", async function () {
      // Whitelist investor1 for transfers
      await securityToken.connect(agent).setTransferWhitelistAddress(investor1.address, true);

      const amount = ethers.parseEther("100");
      await securityToken.connect(investor1).transfer(investor2.address, amount);

      expect(await securityToken.balanceOf(investor2.address)).to.equal(amount);
    });

    it("Should allow agents to manage transfer whitelist", async function () {
      await securityToken.connect(agent).setTransferWhitelistAddress(investor1.address, true);
      expect(await securityToken.isTransferWhitelisted(investor1.address)).to.be.true;

      await securityToken.connect(agent).setTransferWhitelistAddress(investor1.address, false);
      expect(await securityToken.isTransferWhitelisted(investor1.address)).to.be.false;
    });

    it("Should emit TransferWhitelistAddressUpdated event", async function () {
      await expect(
        securityToken.connect(agent).setTransferWhitelistAddress(investor1.address, true)
      ).to.emit(securityToken, "TransferWhitelistAddressUpdated")
       .withArgs(investor1.address, true);
    });
  });

  describe("Transfer Fees", function () {
    beforeEach(async function () {
      // Enable transfer fees (1% = 100 basis points)
      await securityToken.setTransferFees(true, 100, feeCollector.address);
    });

    it("Should enable/disable transfer fees", async function () {
      expect(await securityToken.transferFeesEnabled()).to.be.true;

      const [feePercentage, collector] = await securityToken.getTransferFeeConfig();
      expect(feePercentage).to.equal(100);
      expect(collector).to.equal(feeCollector.address);
    });

    it("Should collect fees on transfers", async function () {
      const transferAmount = ethers.parseEther("100");
      const expectedFee = transferAmount * 100n / 10000n; // 1%
      const expectedReceived = transferAmount - expectedFee;

      await securityToken.connect(investor1).transfer(investor2.address, transferAmount);

      expect(await securityToken.balanceOf(investor2.address)).to.equal(expectedReceived);
      expect(await securityToken.balanceOf(feeCollector.address)).to.equal(expectedFee);
    });

    it("Should emit TransferFeeCollected event", async function () {
      const transferAmount = ethers.parseEther("100");

      await expect(
        securityToken.connect(investor1).transfer(investor2.address, transferAmount)
      ).to.emit(securityToken, "TransferFeeCollected");
    });

    it("Should reject fee percentage over 100%", async function () {
      await expect(
        securityToken.setTransferFees(true, 10001, feeCollector.address)
      ).to.be.revertedWith("SecurityToken: fee percentage cannot exceed 100%");
    });

    it("Should require fee collector to be whitelisted", async function () {
      await expect(
        securityToken.setTransferFees(true, 100, unauthorized.address)
      ).to.be.revertedWith("SecurityToken: fee collector must be whitelisted");
    });
  });

  describe("Combined Features", function () {
    it("Should work with all features enabled", async function () {
      // Enable all features
      await securityToken.setConditionalTransfers(true);
      await securityToken.setTransferWhitelist(true);
      await securityToken.setTransferFees(true, 100, feeCollector.address);

      // Whitelist investor1 for transfers
      await securityToken.connect(agent).setTransferWhitelistAddress(investor1.address, true);

      // Approve transfer
      const amount = ethers.parseEther("100");
      const nonce = await securityToken.getTransferNonce(investor1.address);
      await securityToken.connect(agent).approveTransfer(
        investor1.address,
        investor2.address,
        amount,
        nonce
      );

      // Execute approved transfer
      await securityToken.connect(investor1).executeApprovedTransfer(
        investor2.address,
        amount,
        nonce
      );

      // Check balances (should include fee deduction)
      const expectedFee = amount * 100n / 10000n;
      const expectedReceived = amount - expectedFee;

      expect(await securityToken.balanceOf(investor2.address)).to.equal(expectedReceived);
      expect(await securityToken.balanceOf(feeCollector.address)).to.equal(expectedFee);
    });
  });
});
