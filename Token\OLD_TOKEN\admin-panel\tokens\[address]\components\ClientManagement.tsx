'use client';

import { useState, useEffect } from 'react';

// Extend Window interface for ethereum
declare global {
  interface Window {
    ethereum?: any;
  }
}

interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phoneNumber: string;
  nationality: string;
  kycStatus: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  isWhitelisted: boolean;
  walletAddress?: string;
  agreementAccepted?: boolean;
  createdAt: string;
  updatedAt: string;
  tokenApproval?: {
    id: string;
    approvalStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
    kycApproved: boolean;
    whitelistApproved: boolean;
    approvedBy?: string;
    approvedAt?: string;
    rejectedReason?: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
  } | null;
}

interface TokenClientsData {
  token: {
    id: string;
    name: string;
    symbol: string;
    address: string;
  };
  clients: Client[];
  total: number;
  approved: number;
  pending: number;
  rejected: number;
}

interface ClientManagementProps {
  tokenAddress: string;
}

export default function ClientManagement({ tokenAddress }: ClientManagementProps) {
  const [data, setData] = useState<TokenClientsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'approved' | 'pending' | 'rejected'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [updatingClient, setUpdatingClient] = useState<string | null>(null);
  const [checkingWhitelistStatus, setCheckingWhitelistStatus] = useState(false);
  const [approvingKyc, setApprovingKyc] = useState<string | null>(null);

  useEffect(() => {
    fetchTokenClients();
  }, [tokenAddress]);

  // Function to check whitelist status for a specific address
  const checkWhitelistStatus = async (walletAddress: string) => {
    try {
      if (!window.ethereum || !walletAddress) {
        return false;
      }

      const { ethers } = await import('ethers');
      const provider = new ethers.BrowserProvider(window.ethereum);

      // Import SecurityToken ABI to get whitelist address
      const SecurityTokenABI = await import('../../../../contracts/SecurityToken.json');

      // Connect to the token contract to get whitelist address
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        provider
      );

      // Get the whitelist contract address
      const whitelistAddress = await tokenContract.identityRegistry();
      if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
        return false;
      }

      // Import Whitelist ABI
      const WhitelistABI = await import('../../../../contracts/Whitelist.json');

      // Connect to the whitelist contract
      const whitelistContract = new ethers.Contract(
        whitelistAddress,
        WhitelistABI.abi,
        provider
      );

      // Check if address is whitelisted
      const isWhitelisted = await whitelistContract.isWhitelisted(walletAddress);
      return isWhitelisted;

    } catch (error: any) {
      console.error('Error checking whitelist status:', error);
      return false;
    }
  };

  // Function to check whitelist status for all clients
  const checkAllWhitelistStatuses = async () => {
    if (!data?.clients || checkingWhitelistStatus) return;

    setCheckingWhitelistStatus(true);

    try {
      const updatedClients = await Promise.all(
        data.clients.map(async (client: any) => {
          if (!client.walletAddress) {
            return {
              ...client,
              isWhitelisted: false
            };
          }

          const isWhitelisted = await checkWhitelistStatus(client.walletAddress);
          return {
            ...client,
            isWhitelisted
          };
        })
      );

      // Update the data with whitelist statuses
      setData(prev => prev ? {
        ...prev,
        clients: updatedClients,
        approved: updatedClients.filter(c => c.isWhitelisted).length,
        pending: updatedClients.filter(c => c.walletAddress && !c.isWhitelisted).length,
        rejected: 0 // We don't track rejections yet
      } : null);

    } catch (error) {
      console.error('Error checking whitelist statuses:', error);
    } finally {
      setCheckingWhitelistStatus(false);
    }
  };

  // Use the EXACT same whitelist function from token page
  const whitelistClientAddress = async (addressToWhitelist: string) => {
    try {
      // Use client-side transaction (same as handleWhitelistAddress)
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const { ethers } = await import('ethers');
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Import SecurityToken ABI (correct path from our component location)
      const SecurityTokenABI = await import('../../../../contracts/SecurityToken.json');

      // Connect to the token contract to get whitelist address
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Get the whitelist contract address
      const whitelistAddress = await tokenContract.identityRegistry();
      if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
        throw new Error("No whitelist contract found for this token");
      }

      console.log("Whitelist contract address:", whitelistAddress);

      // Import Whitelist ABI (correct path from our component location)
      const WhitelistABI = await import('../../../../contracts/Whitelist.json');

      // Connect to the whitelist contract
      const whitelistContract = new ethers.Contract(
        whitelistAddress,
        WhitelistABI.abi,
        signer
      );

      // Check if address is already whitelisted before attempting to add
      console.log(`Checking if ${addressToWhitelist} is already whitelisted...`);
      const isAlreadyWhitelisted = await whitelistContract.isWhitelisted(addressToWhitelist);

      if (isAlreadyWhitelisted) {
        console.log(`Address ${addressToWhitelist} is already whitelisted`);
        return {
          success: true,
          txHash: null,
          message: `Address ${addressToWhitelist} is already whitelisted`
        };
      }

      // Add to whitelist with optimized gas settings (same as token page)
      console.log(`Adding ${addressToWhitelist} to whitelist...`);

      const gasLimit = BigInt(300000); // Increased gas limit to 300k
      const gasPrice = ethers.parseUnits("100", "gwei"); // Increased gas price to 100 gwei

      const tx = await whitelistContract.addToWhitelist(addressToWhitelist, {
        gasLimit,
        gasPrice
      });

      console.log(`Transaction submitted. Hash: ${tx.hash}`);

      // Wait for confirmation
      await tx.wait();

      console.log(`Successfully whitelisted address: ${addressToWhitelist}`);

      return {
        success: true,
        txHash: tx.hash,
        message: `Successfully whitelisted address: ${addressToWhitelist}`
      };

    } catch (error: any) {
      console.error('Error whitelisting address:', error);
      return {
        success: false,
        error: error.message || 'Error whitelisting address. Please try again.'
      };
    }
  };

  // Function to force approve KYC for a client
  const forceApproveKyc = async (clientId: string) => {
    try {
      setApprovingKyc(clientId);

      // Find the client to get their wallet address
      const client = data?.clients.find(c => c.id === clientId);
      if (!client) {
        throw new Error('Client not found');
      }

      if (!client.walletAddress) {
        throw new Error('Client has no wallet address connected');
      }

      console.log(`Force approving KYC for client ${client.firstName} ${client.lastName} (${client.walletAddress})`);

      // Step 1: Update KYC status in database
      const dbResponse = await fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          kycStatus: 'APPROVED'
        }),
      });

      if (!dbResponse.ok) {
        throw new Error('Failed to update KYC status in database');
      }

      // Step 2: Approve KYC on blockchain
      if (!window.ethereum) {
        throw new Error('Please install MetaMask to use this feature!');
      }

      const { ethers } = await import('ethers');
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();

      // Import SecurityToken ABI to get whitelist address
      const SecurityTokenABI = await import('../../../../contracts/SecurityToken.json');

      // Connect to the token contract to get whitelist address
      const tokenContract = new ethers.Contract(
        tokenAddress,
        SecurityTokenABI.abi,
        signer
      );

      // Get the whitelist contract address
      const whitelistAddress = await tokenContract.identityRegistry();
      if (!whitelistAddress || whitelistAddress === ethers.ZeroAddress) {
        throw new Error("No whitelist contract found for this token");
      }

      console.log("Whitelist contract address:", whitelistAddress);

      // Import Whitelist ABI
      const WhitelistABI = await import('../../../../contracts/Whitelist.json');

      // Connect to the whitelist contract
      const whitelistContract = new ethers.Contract(
        whitelistAddress,
        WhitelistABI.abi,
        signer
      );

      // Approve KYC on blockchain with optimized gas settings
      console.log(`Approving KYC for ${client.walletAddress} on blockchain...`);

      const gasLimit = BigInt(200000); // 200k gas limit
      const gasPrice = ethers.parseUnits("50", "gwei"); // 50 gwei gas price

      const tx = await whitelistContract.approveKyc(client.walletAddress, {
        gasLimit,
        gasPrice
      });

      console.log(`KYC approval transaction submitted. Hash: ${tx.hash}`);

      // Wait for confirmation
      await tx.wait();

      console.log(`Successfully approved KYC for address: ${client.walletAddress}`);

      // Show success message
      alert(`✅ KYC Force Approved Successfully!\n\n` +
            `Client: ${client.firstName} ${client.lastName}\n` +
            `Wallet: ${client.walletAddress}\n` +
            `Transaction: ${tx.hash}\n\n` +
            `KYC status updated in database and blockchain.`);

      // Refresh the data to show updated status
      await fetchTokenClients();

    } catch (error: any) {
      console.error('Error force approving KYC:', error);
      alert(`❌ Error: ${error.message || 'Failed to force approve KYC'}`);
    } finally {
      setApprovingKyc(null);
    }
  };

  const fetchTokenClients = async () => {
    try {
      setLoading(true);

      // Fetch token details and all clients in parallel
      const [tokenResponse, clientsResponse] = await Promise.all([
        fetch(`/api/tokens?source=database`),
        fetch('/api/clients')
      ]);

      if (!clientsResponse.ok) {
        throw new Error('Failed to fetch clients');
      }

      const clientsData = await clientsResponse.json();
      const clientsArray = clientsData.clients || [];

      // Find the current token details
      let tokenInfo = {
        id: 'temp-id',
        name: 'Unknown Token',
        symbol: 'TOKEN',
        address: tokenAddress
      };

      if (tokenResponse.ok) {
        const tokensData = await tokenResponse.json();
        const currentToken = tokensData.find((token: any) =>
          token.address.toLowerCase() === tokenAddress.toLowerCase()
        );
        if (currentToken) {
          tokenInfo = {
            id: currentToken.id,
            name: currentToken.name,
            symbol: currentToken.symbol,
            address: tokenAddress
          };
        }
      }

      // Simulate token-specific data structure
      const result = {
        token: tokenInfo,
        clients: clientsArray.map((client: any) => ({
          ...client,
          tokenApproval: null // No approval data yet - will be populated when database schema is ready
        })),
        total: clientsArray.length,
        approved: 0,
        pending: clientsArray.length,
        rejected: 0
      };

      setData(result);

      // After loading clients, check their whitelist statuses
      setTimeout(() => {
        checkAllWhitelistStatuses();
      }, 500); // Small delay to ensure data is set

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const updateClientApproval = async (
    clientId: string,
    updates: {
      approvalStatus?: 'PENDING' | 'APPROVED' | 'REJECTED';
      kycApproved?: boolean;
      whitelistApproved?: boolean;
      notes?: string;
      rejectedReason?: string;
    }
  ) => {
    try {
      setUpdatingClient(clientId);

      // Find the client to get their wallet address
      const client = data?.clients.find(c => c.id === clientId);
      if (!client) {
        throw new Error('Client not found');
      }

      if (!client.walletAddress) {
        throw new Error('Client has no wallet address connected');
      }

      // If approving, whitelist the client's wallet address on blockchain
      if (updates.approvalStatus === 'APPROVED' && updates.whitelistApproved) {
        console.log(`Whitelisting client ${client.firstName} ${client.lastName} (${client.walletAddress}) for token ${tokenAddress}`);

        // Use the same whitelist logic as the existing token page
        const whitelistResult = await whitelistClientAddress(client.walletAddress);

        if (!whitelistResult.success) {
          throw new Error(whitelistResult.error || 'Failed to whitelist address');
        }

        // Show success message with transaction hash
        alert(`✅ Client approved and whitelisted successfully!\n\n` +
              `Client: ${client.firstName} ${client.lastName}\n` +
              `Wallet: ${client.walletAddress}\n` +
              `Transaction: ${whitelistResult.txHash}\n\n` +
              `The client can now participate in this token.`);
      } else if (updates.approvalStatus === 'REJECTED') {
        // For rejection, just show confirmation
        alert(`❌ Client ${client.firstName} ${client.lastName} has been rejected for this token.`);
      } else {
        // For other updates
        alert(`✅ Client ${client.firstName} ${client.lastName} status updated successfully.`);
      }

      // TODO: When database schema is ready, also save to database
      // await fetch(`/api/tokens/${tokenAddress}/clients/${clientId}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ ...updates, approvedBy: 'admin' })
      // });

      // Refresh whitelist statuses to show updated blockchain state
      await checkAllWhitelistStatuses();
    } catch (err) {
      console.error('Error updating client approval:', err);
      setError(err instanceof Error ? err.message : 'Failed to update client approval');
      alert(`❌ Error: ${err instanceof Error ? err.message : 'Failed to update client approval'}`);
    } finally {
      setUpdatingClient(null);
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'PENDING':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getKycStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
      case 'EXPIRED':
        return 'bg-red-100 text-red-800';
      case 'IN_REVIEW':
        return 'bg-blue-100 text-blue-800';
      case 'PENDING':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredClients = data?.clients.filter(client => {
    // Filter by status
    const statusMatch = filter === 'all' ||
      (filter === 'approved' && client.tokenApproval?.approvalStatus === 'APPROVED') ||
      (filter === 'pending' && (!client.tokenApproval || client.tokenApproval.approvalStatus === 'PENDING')) ||
      (filter === 'rejected' && client.tokenApproval?.approvalStatus === 'REJECTED');

    // Filter by search term
    const searchMatch = !searchTerm ||
      `${client.firstName} ${client.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.walletAddress?.toLowerCase().includes(searchTerm.toLowerCase());

    return statusMatch && searchMatch;
  }) || [];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong className="font-bold">Error:</strong>
        <span className="block sm:inline"> {error}</span>
        <button
          onClick={fetchTokenClients}
          className="mt-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return <div>No data available</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header with stats */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-semibold">
            Client Management for {data.token.name} ({data.token.symbol})
          </h3>
          <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
            Demo Mode
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex justify-between items-start">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  <strong>🚀 Blockchain Integration Active:</strong> Status shows real blockchain whitelist data.
                  Click "Approve" to whitelist clients on-chain!
                </p>
              </div>
            </div>
            <button
              onClick={checkAllWhitelistStatuses}
              disabled={checkingWhitelistStatus}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
            >
              {checkingWhitelistStatus ? 'Checking...' : 'Refresh Status'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{data.total}</div>
            <div className="text-sm text-gray-600">Total Clients</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{data.approved}</div>
            <div className="text-sm text-gray-600">Approved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{data.pending}</div>
            <div className="text-sm text-gray-600">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{data.rejected}</div>
            <div className="text-sm text-gray-600">Rejected</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          <div className="flex space-x-2">
            {(['all', 'approved', 'pending', 'rejected'] as const).map((filterOption) => (
              <button
                key={filterOption}
                onClick={() => setFilter(filterOption)}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  filter === filterOption
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {filterOption.charAt(0).toUpperCase() + filterOption.slice(1)}
              </button>
            ))}
          </div>

          <input
            type="text"
            placeholder="Search clients..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded text-sm"
          />
        </div>
      </div>

      {/* Clients table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  KYC Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Whitelist Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Wallet
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredClients.map((client) => (
                <tr key={client.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {client.firstName} {client.lastName}
                      </div>
                      <div className="text-sm text-gray-500">{client.email}</div>
                      <div className="text-xs text-gray-400">{client.nationality}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getKycStatusColor(client.kycStatus)}`}>
                      {client.kycStatus}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {client.walletAddress ? (
                      <div className="space-y-1">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          client.isWhitelisted === true
                            ? 'bg-green-100 text-green-800'
                            : client.isWhitelisted === false
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {client.isWhitelisted === true
                            ? '✓ WHITELISTED'
                            : client.isWhitelisted === false
                            ? '✗ NOT WHITELISTED'
                            : '⏳ CHECKING...'}
                        </span>
                        {client.isWhitelisted === true && (
                          <div className="text-xs text-green-600">Can participate in token</div>
                        )}
                        {client.isWhitelisted === false && (
                          <div className="text-xs text-red-600">Cannot participate yet</div>
                        )}
                      </div>
                    ) : (
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                        NO WALLET CONNECTED
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 font-mono">
                      {client.walletAddress ? (
                        <div className="flex items-center space-x-2">
                          <span className="text-green-600">✓</span>
                          <span>{`${client.walletAddress.slice(0, 6)}...${client.walletAddress.slice(-4)}`}</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <span className="text-red-600">✗</span>
                          <span className="text-gray-500">Not connected</span>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {updatingClient === client.id ? (
                      <div className="text-gray-500">Updating...</div>
                    ) : approvingKyc === client.id ? (
                      <div className="text-blue-500">Approving KYC...</div>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex space-x-2">
                          {(!client.tokenApproval || client.tokenApproval.approvalStatus !== 'APPROVED') && (
                            <button
                              onClick={() => updateClientApproval(client.id, {
                                approvalStatus: 'APPROVED',
                                kycApproved: true,
                                whitelistApproved: true
                              })}
                              disabled={!client.walletAddress}
                              className={`px-2 py-1 rounded text-xs ${
                                client.walletAddress
                                  ? 'bg-green-600 text-white hover:bg-green-700'
                                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                              }`}
                              title={!client.walletAddress ? 'Client must connect wallet first' : 'Approve and whitelist client'}
                            >
                              Approve
                            </button>
                          )}
                          {(!client.tokenApproval || client.tokenApproval.approvalStatus !== 'REJECTED') && (
                            <button
                              onClick={() => updateClientApproval(client.id, {
                                approvalStatus: 'REJECTED',
                                kycApproved: false,
                                whitelistApproved: false
                              })}
                              className="px-2 py-1 rounded text-xs bg-red-600 text-white hover:bg-red-700"
                            >
                              Reject
                            </button>
                          )}
                        </div>

                        {/* Force KYC Approve button */}
                        {client.kycStatus !== 'APPROVED' && (
                          <button
                            onClick={() => forceApproveKyc(client.id)}
                            disabled={!client.walletAddress || approvingKyc === client.id}
                            className={`px-2 py-1 rounded text-xs w-full ${
                              client.walletAddress
                                ? 'bg-blue-600 text-white hover:bg-blue-700'
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            }`}
                            title={!client.walletAddress ? 'Client must connect wallet first' : 'Force approve KYC in database and blockchain'}
                          >
                            Force KYC Approve
                          </button>
                        )}

                        {client.kycStatus === 'APPROVED' && (
                          <div className="text-xs text-green-600 text-center">
                            ✓ KYC Approved
                          </div>
                        )}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredClients.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No clients found matching the current filters.
          </div>
        )}
      </div>
    </div>
  );
}
