// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "./interfaces/IWhitelist.sol";

/**
 * @title WhitelistV2
 * @dev Implementation of the whitelist functionality for ERC-3643 compliant tokens with KYC support
 */
contract WhitelistV2 is 
    Initializable, 
    AccessControlUpgradeable, 
    UUPSUpgradeable,
    IWhitelist 
{
    bytes32 public constant AGENT_ROLE = keccak256("AGENT_ROLE");
    
    // Mapping of whitelisted addresses
    mapping(address => bool) private _whitelisted;
    
    // Mapping of frozen addresses
    mapping(address => bool) private _frozen;
    
    // Mapping of KYC approved addresses (added in V2)
    mapping(address => bool) private _kycApproved;
    
    // NOTE: Events are defined in IWhitelist interface, we don't need to redeclare them here
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    /**
     * @dev Initialize the contract
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     */
    function initialize(address admin) public initializer {
        __AccessControl_init();
        __UUPSUpgradeable_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
    }
    
    /**
     * @dev Check if an address is whitelisted
     * @param _address The address to check
     * @return bool True if the address is whitelisted, false otherwise
     */
    function isWhitelisted(address _address) public view override returns (bool) {
        return _whitelisted[_address];
    }
    
    /**
     * @dev Add an address to the whitelist
     * @param _address The address to add to the whitelist
     */
    function addToWhitelist(address _address) public override onlyRole(AGENT_ROLE) {
        require(!_whitelisted[_address], "Whitelist: address already whitelisted");
        _whitelisted[_address] = true;
        emit AddedToWhitelist(_address);
    }
    
    /**
     * @dev Remove an address from the whitelist
     * @param _address The address to remove from the whitelist
     */
    function removeFromWhitelist(address _address) public override onlyRole(AGENT_ROLE) {
        require(_whitelisted[_address], "Whitelist: address not whitelisted");
        _whitelisted[_address] = false;
        emit RemovedFromWhitelist(_address);
    }
    
    /**
     * @dev Check if an address is frozen
     * @param _address The address to check
     * @return bool True if the address is frozen, false otherwise
     */
    function isFrozen(address _address) public view override returns (bool) {
        return _frozen[_address];
    }
    
    /**
     * @dev Freeze an address
     * @param _address The address to freeze
     */
    function freezeAddress(address _address) public override onlyRole(AGENT_ROLE) {
        require(!_frozen[_address], "Whitelist: address already frozen");
        _frozen[_address] = true;
        emit AddressFrozen(_address);
    }
    
    /**
     * @dev Unfreeze an address
     * @param _address The address to unfreeze
     */
    function unfreezeAddress(address _address) public override onlyRole(AGENT_ROLE) {
        require(_frozen[_address], "Whitelist: address not frozen");
        _frozen[_address] = false;
        emit AddressUnfrozen(_address);
    }
    
    /**
     * @dev Function to authorize an upgrade
     * @param newImplementation The address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation) internal override onlyRole(DEFAULT_ADMIN_ROLE) {}
    
    /**
     * @dev Batch add addresses to whitelist
     * @param _addresses The addresses to add to the whitelist
     */
    function batchAddToWhitelist(address[] calldata _addresses) external onlyRole(AGENT_ROLE) {
        for (uint256 i = 0; i < _addresses.length; i++) {
            if (!_whitelisted[_addresses[i]]) {
                _whitelisted[_addresses[i]] = true;
                emit AddedToWhitelist(_addresses[i]);
            }
        }
    }
    
    /**
     * @dev Batch remove addresses from whitelist
     * @param _addresses The addresses to remove from the whitelist
     */
    function batchRemoveFromWhitelist(address[] calldata _addresses) external onlyRole(AGENT_ROLE) {
        for (uint256 i = 0; i < _addresses.length; i++) {
            if (_whitelisted[_addresses[i]]) {
                _whitelisted[_addresses[i]] = false;
                emit RemovedFromWhitelist(_addresses[i]);
            }
        }
    }
    
    /**
     * @dev Batch freeze addresses
     * @param _addresses The addresses to freeze
     */
    function batchFreezeAddresses(address[] calldata _addresses) external onlyRole(AGENT_ROLE) {
        for (uint256 i = 0; i < _addresses.length; i++) {
            if (!_frozen[_addresses[i]]) {
                _frozen[_addresses[i]] = true;
                emit AddressFrozen(_addresses[i]);
            }
        }
    }
    
    /**
     * @dev Batch unfreeze addresses
     * @param _addresses The addresses to unfreeze
     */
    function batchUnfreezeAddresses(address[] calldata _addresses) external onlyRole(AGENT_ROLE) {
        for (uint256 i = 0; i < _addresses.length; i++) {
            if (_frozen[_addresses[i]]) {
                _frozen[_addresses[i]] = false;
                emit AddressUnfrozen(_addresses[i]);
            }
        }
    }

    /**
     * @dev Grant the AGENT_ROLE to an address
     * @param _agent The address to grant the AGENT_ROLE to
     */
    function addAgent(address _agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        grantRole(AGENT_ROLE, _agent);
    }

    /**
     * @dev Revoke the AGENT_ROLE from an address
     * @param _agent The address to revoke the AGENT_ROLE from
     */
    function removeAgent(address _agent) external onlyRole(DEFAULT_ADMIN_ROLE) {
        revokeRole(AGENT_ROLE, _agent);
    }

    /**
     * @dev Special initialization function for factory deployment
     * This function can only be called during initialization
     * @param _admin The address to be granted DEFAULT_ADMIN_ROLE and AGENT_ROLE
     */
    function initializeWithAgent(address _admin) external initializer {
        __AccessControl_init();
        __UUPSUpgradeable_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(AGENT_ROLE, _admin);
    }

    /**
     * @dev Check if an address is KYC approved
     * @param _address The address to check
     * @return bool True if the address is KYC approved, false otherwise
     */
    function isKycApproved(address _address) public view override returns (bool) {
        return _kycApproved[_address];
    }

    /**
     * @dev Approve KYC for an address
     * @param _address The address to approve KYC for
     */
    function approveKyc(address _address) public override onlyRole(AGENT_ROLE) {
        require(!_kycApproved[_address], "Whitelist: address already KYC approved");
        _kycApproved[_address] = true;
        emit KycApproved(_address);
    }

    /**
     * @dev Revoke KYC approval for an address
     * @param _address The address to revoke KYC approval from
     */
    function revokeKyc(address _address) public override onlyRole(AGENT_ROLE) {
        require(_kycApproved[_address], "Whitelist: address not KYC approved");
        _kycApproved[_address] = false;
        emit KycRevoked(_address);
    }

    /**
     * @dev Batch approve KYC for addresses
     * @param _addresses The addresses to approve KYC for
     */
    function batchApproveKyc(address[] calldata _addresses) external override onlyRole(AGENT_ROLE) {
        for (uint256 i = 0; i < _addresses.length; i++) {
            if (!_kycApproved[_addresses[i]]) {
                _kycApproved[_addresses[i]] = true;
                emit KycApproved(_addresses[i]);
            }
        }
    }

    /**
     * @dev Batch revoke KYC approval for addresses
     * @param _addresses The addresses to revoke KYC approval from
     */
    function batchRevokeKyc(address[] calldata _addresses) external override onlyRole(AGENT_ROLE) {
        for (uint256 i = 0; i < _addresses.length; i++) {
            if (_kycApproved[_addresses[i]]) {
                _kycApproved[_addresses[i]] = false;
                emit KycRevoked(_addresses[i]);
            }
        }
    }
}