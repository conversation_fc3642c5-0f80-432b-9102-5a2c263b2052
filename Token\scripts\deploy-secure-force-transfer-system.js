const { ethers, upgrades } = require("hardhat");
const fs = require('fs');
const path = require('path');

async function main() {
    console.log("🚀 Deploying Secure Force Transfer System...");
    
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

    try {
        // Step 1: Deploy ClaimRegistry
        console.log("\n📋 Deploying ClaimRegistry...");
        const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
        const claimRegistry = await upgrades.deployProxy(
            ClaimRegistry,
            [deployer.address],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0 // Disable timeout for large contracts
            }
        );
        await claimRegistry.waitForDeployment();
        const claimRegistryAddress = await claimRegistry.getAddress();
        console.log("✅ ClaimRegistry deployed to:", claimRegistryAddress);

        // Step 2: Deploy IdentityRegistry
        console.log("\n🆔 Deploying IdentityRegistry...");
        const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
        const identityRegistry = await upgrades.deployProxy(
            IdentityRegistry,
            [deployer.address, claimRegistryAddress],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0
            }
        );
        await identityRegistry.waitForDeployment();
        const identityRegistryAddress = await identityRegistry.getAddress();
        console.log("✅ IdentityRegistry deployed to:", identityRegistryAddress);

        // Step 3: Deploy Compliance
        console.log("\n📊 Deploying Compliance...");
        const Compliance = await ethers.getContractFactory("Compliance");
        const compliance = await upgrades.deployProxy(
            Compliance,
            [deployer.address, identityRegistryAddress],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0
            }
        );
        await compliance.waitForDeployment();
        const complianceAddress = await compliance.getAddress();
        console.log("✅ Compliance deployed to:", complianceAddress);

        // Step 4: Deploy SecurityToken with Enhanced Force Transfer
        console.log("\n🔒 Deploying Enhanced SecurityToken...");
        const SecurityToken = await ethers.getContractFactory("SecurityToken");
        
        const tokenParams = {
            name: "Secure Test Token",
            symbol: "STT",
            decimals: 0,
            maxSupply: ethers.parseUnits("1000000", 0),
            identityRegistry: identityRegistryAddress,
            compliance: complianceAddress,
            admin: deployer.address,
            tokenPrice: "1.00 USD",
            bonusTiers: "Early: 10%, Standard: 5%",
            tokenDetails: "Enhanced security token with graduated force transfer compliance",
            tokenImageUrl: ""
        };

        const securityToken = await upgrades.deployProxy(
            SecurityToken,
            [
                tokenParams.name,
                tokenParams.symbol,
                tokenParams.decimals,
                tokenParams.maxSupply,
                tokenParams.identityRegistry,
                tokenParams.compliance,
                tokenParams.admin,
                tokenParams.tokenPrice,
                tokenParams.bonusTiers,
                tokenParams.tokenDetails,
                tokenParams.tokenImageUrl
            ],
            { 
                initializer: "initialize", 
                kind: "uups",
                timeout: 0,
                pollingInterval: 5000 // 5 second polling for large contracts
            }
        );
        await securityToken.waitForDeployment();
        const securityTokenAddress = await securityToken.getAddress();
        console.log("✅ Enhanced SecurityToken deployed to:", securityTokenAddress);

        // Step 5: Set up additional authorizers
        console.log("\n👥 Setting up force transfer authorizers...");
        const FORCE_TRANSFER_AUTHORIZER_ROLE = await securityToken.FORCE_TRANSFER_AUTHORIZER_ROLE();
        
        // Grant authorizer role to deployer (already done in initialization)
        console.log("✅ Deployer already has FORCE_TRANSFER_AUTHORIZER_ROLE");

        // Step 6: Verify force transfer configuration
        console.log("\n🔍 Verifying force transfer configuration...");
        const ForceTransferType = {
            EMERGENCY_SECURITY: 0,
            REGULATORY_ORDER: 1,
            CORPORATE_ACTION: 2,
            RECOVERY_ASSISTANCE: 3
        };

        for (const [typeName, typeValue] of Object.entries(ForceTransferType)) {
            const requiredSigs = await securityToken.getRequiredSignatures(typeValue);
            console.log(`✅ ${typeName}: ${requiredSigs} signature(s) required`);
        }

        // Step 7: Test basic functionality
        console.log("\n🧪 Testing basic functionality...");
        
        // Register a test identity
        await identityRegistry.registerIdentity(deployer.address, 840); // USA
        await identityRegistry.addToWhitelist(deployer.address);
        await identityRegistry.approveKyc(deployer.address);
        console.log("✅ Test identity registered and whitelisted");

        // Mint some tokens
        const AGENT_ROLE = await securityToken.AGENT_ROLE();
        await securityToken.mint(deployer.address, 1000);
        const balance = await securityToken.balanceOf(deployer.address);
        console.log(`✅ Minted tokens. Balance: ${balance}`);

        // Test legacy force transfer still works
        const testRecipient = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6"; // Random address
        await identityRegistry.registerIdentity(testRecipient, 840);
        await identityRegistry.addToWhitelist(testRecipient);
        await identityRegistry.approveKyc(testRecipient);
        
        await securityToken.forcedTransfer(deployer.address, testRecipient, 100);
        const recipientBalance = await securityToken.balanceOf(testRecipient);
        console.log(`✅ Legacy force transfer works. Recipient balance: ${recipientBalance}`);

        // Step 8: Save deployment information
        const deploymentInfo = {
            network: "hardhat",
            chainId: (await ethers.provider.getNetwork()).chainId.toString(),
            timestamp: new Date().toISOString(),
            deployer: deployer.address,
            contracts: {
                claimRegistry: claimRegistryAddress,
                identityRegistry: identityRegistryAddress,
                compliance: complianceAddress,
                securityToken: securityTokenAddress
            },
            tokenInfo: {
                ...tokenParams,
                maxSupply: tokenParams.maxSupply.toString()
            },
            forceTransferConfig: {
                EMERGENCY_SECURITY: await securityToken.getRequiredSignatures(0),
                REGULATORY_ORDER: await securityToken.getRequiredSignatures(1),
                CORPORATE_ACTION: await securityToken.getRequiredSignatures(2),
                RECOVERY_ASSISTANCE: await securityToken.getRequiredSignatures(3)
            },
            features: {
                secureForceTransfer: true,
                graduatedCompliance: true,
                multiSignatureAuth: true,
                timeDelayedExecution: true,
                backwardCompatible: true,
                enhancedAuditTrail: true
            }
        };

        const deploymentPath = path.join(__dirname, '../deployments/secure-force-transfer-system.json');
        fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
        console.log(`\n💾 Deployment info saved to: ${deploymentPath}`);

        // Step 9: Display summary
        console.log("\n🎉 DEPLOYMENT COMPLETE!");
        console.log("=" .repeat(60));
        console.log("📋 ClaimRegistry:", claimRegistryAddress);
        console.log("🆔 IdentityRegistry:", identityRegistryAddress);
        console.log("📊 Compliance:", complianceAddress);
        console.log("🔒 Enhanced SecurityToken:", securityTokenAddress);
        console.log("=" .repeat(60));
        console.log("\n✨ New Features Available:");
        console.log("• Graduated force transfer compliance");
        console.log("• Multi-signature authorization");
        console.log("• Time-delayed execution for non-emergency transfers");
        console.log("• Enhanced audit trail with detailed events");
        console.log("• Backward compatibility with legacy force transfers");
        console.log("\n🔧 Next Steps:");
        console.log("1. Update admin panel to use new contract addresses");
        console.log("2. Add additional force transfer authorizers if needed");
        console.log("3. Configure signature requirements for your organization");
        console.log("4. Test the new secure force transfer workflow");

        return {
            claimRegistry: claimRegistryAddress,
            identityRegistry: identityRegistryAddress,
            compliance: complianceAddress,
            securityToken: securityTokenAddress
        };

    } catch (error) {
        console.error("❌ Deployment failed:", error);
        throw error;
    }
}

// Execute deployment
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;
