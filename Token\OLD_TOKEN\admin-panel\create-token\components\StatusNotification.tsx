import React from 'react';
import { DeploymentStep } from '../types';

interface StatusNotificationProps {
  type: 'error' | 'success' | 'warning' | 'info';
  message: string;
  deploymentStep?: DeploymentStep;
  tokenSymbol?: string;
  onAddManually?: () => void;
}

/**
 * StatusNotification Component
 * 
 * Displays notifications like errors, warnings, or success messages
 */
const StatusNotification: React.FC<StatusNotificationProps> = ({
  type,
  message,
  deploymentStep,
  tokenSymbol,
  onAddManually
}) => {
  // Determine styling based on notification type
  const getBorderColor = () => {
    switch (type) {
      case 'error': return 'border-red-500';
      case 'success': return 'border-green-500';
      case 'warning': return 'border-yellow-500';
      case 'info': return 'border-blue-500';
      default: return 'border-gray-500';
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'error': return 'bg-red-100';
      case 'success': return 'bg-green-100';
      case 'warning': return 'bg-yellow-100';
      case 'info': return 'bg-blue-100';
      default: return 'bg-gray-100';
    }
  };

  const getTextColor = () => {
    switch (type) {
      case 'error': return 'text-red-700';
      case 'success': return 'text-green-700';
      case 'warning': return 'text-yellow-700';
      case 'info': return 'text-blue-700';
      default: return 'text-gray-700';
    }
  };

  const containerClasses = `${getBackgroundColor()} border-l-4 ${getBorderColor()} ${getTextColor()} p-4 mb-6`;

  return (
    <div className={containerClasses}>
      <p className="whitespace-pre-wrap">{message}</p>
      
      {/* Show "Add Manually" button if deployment failed but we have a token symbol */}
      {deploymentStep === 'failed' && tokenSymbol && onAddManually && (
        <div className="mt-4">
          <p>
            If your token was actually created despite the error, you can try adding it manually to the dashboard:
          </p>
          <button
            onClick={onAddManually}
            className="mt-2 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm"
          >
            Try to find token "{tokenSymbol}" on dashboard
          </button>
        </div>
      )}
    </div>
  );
};

export default StatusNotification;