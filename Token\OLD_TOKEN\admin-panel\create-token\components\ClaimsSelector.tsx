import React, { useState, useEffect } from 'react';

interface ClaimType {
  id: string;
  name: string;
  description: string;
  creator: string;
  createdAt: string;
  active: boolean;
}

interface ClaimsSelectorProps {
  selectedClaims: string[];
  onChange: (selectedClaims: string[]) => void;
  disabled?: boolean;
}

const ClaimsSelector: React.FC<ClaimsSelectorProps> = ({
  selectedClaims,
  onChange,
  disabled = false
}) => {
  const [claimTypes, setClaimTypes] = useState<ClaimType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newClaimType, setNewClaimType] = useState({
    name: '',
    description: ''
  });
  const [creating, setCreating] = useState(false);

  // Fetch available claim types
  const fetchClaimTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/claim-types?limit=100');
      if (response.ok) {
        const data = await response.json();
        setClaimTypes(data.claimTypes || []);
      } else {
        setError('Failed to fetch claim types');
      }
    } catch (err) {
      setError('Error loading claim types');
      console.error('Error fetching claim types:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClaimTypes();
  }, []);

  // Handle claim selection
  const handleClaimToggle = (claimId: string) => {
    if (disabled) return;
    
    const newSelection = selectedClaims.includes(claimId)
      ? selectedClaims.filter(id => id !== claimId)
      : [...selectedClaims, claimId];
    
    onChange(newSelection);
  };

  // Create new claim type
  const handleCreateClaimType = async () => {
    if (!newClaimType.name.trim() || !newClaimType.description.trim()) {
      alert('Please fill in both name and description');
      return;
    }

    try {
      setCreating(true);
      const response = await fetch('/api/claim-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newClaimType),
      });

      if (response.ok) {
        const result = await response.json();
        setNewClaimType({ name: '', description: '' });
        setShowCreateForm(false);
        await fetchClaimTypes(); // Refresh the list
        
        // Auto-select the newly created claim
        if (result.claimTypeId) {
          onChange([...selectedClaims, result.claimTypeId]);
        }
      } else {
        const errorData = await response.json();
        alert(`Failed to create claim type: ${errorData.error}`);
      }
    } catch (err) {
      console.error('Error creating claim type:', err);
      alert('Error creating claim type');
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading claim types...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-600">{error}</p>
        <button
          onClick={fetchClaimTypes}
          className="mt-2 text-sm text-red-700 hover:text-red-900 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Available Claim Types */}
      {claimTypes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {claimTypes.map((claimType) => (
            <div
              key={claimType.id}
              className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedClaims.includes(claimType.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => handleClaimToggle(claimType.id)}
            >
              <input
                type="checkbox"
                className="mt-1 h-4 w-4 text-blue-600 rounded"
                checked={selectedClaims.includes(claimType.id)}
                onChange={() => {}} // Handled by div onClick
                disabled={disabled}
              />
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-900 cursor-pointer">
                  {claimType.name}
                  <span className="text-xs text-gray-500 ml-1">(ID: {claimType.id})</span>
                </label>
                <p className="text-xs text-gray-600">{claimType.description}</p>
                <p className="text-xs text-gray-400 mt-1">
                  Created: {new Date(claimType.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No claim types available.</p>
          <p className="text-sm">Create your first claim type below.</p>
        </div>
      )}

      {/* Create New Claim Type */}
      <div className="border-t pt-4">
        {!showCreateForm ? (
          <button
            onClick={() => setShowCreateForm(true)}
            disabled={disabled}
            className="text-sm text-blue-600 hover:text-blue-800 underline disabled:opacity-50"
          >
            + Create New Claim Type
          </button>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Create New Claim Type</h4>
            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={newClaimType.name}
                  onChange={(e) => setNewClaimType({ ...newClaimType, name: e.target.value })}
                  placeholder="e.g., Accredited Investor"
                  className="w-full text-sm border border-gray-300 rounded px-3 py-2"
                  disabled={creating}
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={newClaimType.description}
                  onChange={(e) => setNewClaimType({ ...newClaimType, description: e.target.value })}
                  placeholder="e.g., Qualified as an accredited investor under SEC regulations"
                  className="w-full text-sm border border-gray-300 rounded px-3 py-2 h-20 resize-none"
                  disabled={creating}
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleCreateClaimType}
                  disabled={creating || !newClaimType.name.trim() || !newClaimType.description.trim()}
                  className="text-sm bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
                >
                  {creating ? 'Creating...' : 'Create'}
                </button>
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    setNewClaimType({ name: '', description: '' });
                  }}
                  disabled={creating}
                  className="text-sm bg-gray-300 text-gray-700 px-3 py-2 rounded hover:bg-gray-400 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Selection Summary */}
      {selectedClaims.length > 0 && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-800">
            <strong>Selected:</strong> {selectedClaims.length} claim type{selectedClaims.length !== 1 ? 's' : ''}
          </p>
          <p className="text-xs text-green-700 mt-1">
            Investors will need to have ALL selected claims to access this token.
          </p>
        </div>
      )}
    </div>
  );
};

export default ClaimsSelector;
